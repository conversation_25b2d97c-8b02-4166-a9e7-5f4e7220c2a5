'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useCasesApi } from '@/hooks/useCasesApi';
import { useClientsApi } from '@/hooks/useClientsApi';
import { useTasksApi } from '@/hooks/useTasksApi';

export default function DashboardTestPage() {
  const { getAllCases } = useCasesApi();
  const { getAllClients } = useClientsApi();
  const { getAllTasks } = useTasksApi();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<{
    casesTest: { success: boolean; message: string; data: unknown[] | null };
    clientsTest: { success: boolean; message: string; data: unknown[] | null };
    tasksTest: { success: boolean; message: string; data: unknown[] | null };
    directCasesTest: { success: boolean; message: string; data: unknown[] | null };
  }>({
    casesTest: { success: false, message: 'Not started', data: null },
    clientsTest: { success: false, message: 'Not started', data: null },
    tasksTest: { success: false, message: 'Not started', data: null },
    directCasesTest: { success: false, message: 'Not started', data: null },
  });

  useEffect(() => {
    const runTests = async () => {
      setLoading(true);
      setError(null);

      try {
        // Test 1: Test cases API with hooks
        try {
          const casesData = await getAllCases();
          setTestResults(prev => ({
            ...prev,
            casesTest: {
              success: true,
              message: `Successfully fetched ${casesData?.length || 0} cases`,
              data: casesData || []
            }
          }));
        } catch (err) {
          console.error('Cases test failed:', err);
          setTestResults(prev => ({
            ...prev,
            casesTest: {
              success: false,
              message: err instanceof Error ? err.message : 'Failed to fetch cases',
              data: null
            }
          }));
        }

        // Test 2: Test clients API with hooks
        try {
          const clientsData = await getAllClients();
          setTestResults(prev => ({
            ...prev,
            clientsTest: {
              success: true,
              message: `Successfully fetched ${clientsData?.length || 0} clients`,
              data: clientsData || []
            }
          }));
        } catch (err) {
          console.error('Clients test failed:', err);
          setTestResults(prev => ({
            ...prev,
            clientsTest: {
              success: false,
              message: err instanceof Error ? err.message : 'Failed to fetch clients',
              data: null
            }
          }));
        }

        // Test 3: Test tasks API with hooks
        try {
          const tasksData = await getAllTasks();
          setTestResults(prev => ({
            ...prev,
            tasksTest: {
              success: true,
              message: `Successfully fetched ${tasksData?.length || 0} tasks`,
              data: tasksData || []
            }
          }));
        } catch (err) {
          console.error('Tasks test failed:', err);
          setTestResults(prev => ({
            ...prev,
            tasksTest: {
              success: false,
              message: err instanceof Error ? err.message : 'Failed to fetch tasks',
              data: null
            }
          }));
        }

        // Test 4: Test direct cases endpoint
        try {
          const response = await fetch('/api/cases-test', {
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include'
          });

          if (!response.ok) {
            throw new Error(`Error ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          setTestResults(prev => ({
            ...prev,
            directCasesTest: {
              success: data.success || false,
              message: data.message || `Fetched ${data.count || 0} cases directly`,
              data: data.data || null
            }
          }));
        } catch (err) {
          console.error('Direct cases test failed:', err);
          setTestResults(prev => ({
            ...prev,
            directCasesTest: {
              success: false,
              message: err instanceof Error ? err.message : 'Failed to fetch cases directly',
              data: null
            }
          }));
        }

      } catch (err) {
        console.error('Test failed:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    runTests();
  }, [getAllCases, getAllClients, getAllTasks]);

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Dashboard Schema Test</h1>
        <div className="flex gap-2">
          <Button onClick={() => window.location.reload()}>
            Refresh Tests
          </Button>
          <Button variant="outline" asChild>
            <Link href="/dashboard">Return to Dashboard</Link>
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>Error: {error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Cases Test Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <span className={testResults.casesTest.success ? "text-green-500" : "text-red-500"}>●</span>
              <span className="ml-2">Cases API Test</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-2">{testResults.casesTest.message}</p>
            {testResults.casesTest.data && (
              <div className="mt-4">
                <p className="font-semibold">Cases Count: {testResults.casesTest.data.length}</p>
                <div className="mt-2 max-h-32 overflow-y-auto bg-gray-50 p-2 rounded text-xs">
                  <pre>{JSON.stringify(testResults.casesTest.data.slice(0, 2), null, 2)}</pre>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Direct Cases Test Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <span className={testResults.directCasesTest.success ? "text-green-500" : "text-red-500"}>●</span>
              <span className="ml-2">Direct Cases API Test</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-2">{testResults.directCasesTest.message}</p>
            {testResults.directCasesTest.data && (
              <div className="mt-4">
                <p className="font-semibold">Cases Count: {testResults.directCasesTest.data.length}</p>
                <div className="mt-2 max-h-32 overflow-y-auto bg-gray-50 p-2 rounded text-xs">
                  <pre>{JSON.stringify(testResults.directCasesTest.data.slice(0, 2), null, 2)}</pre>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Clients Test Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <span className={testResults.clientsTest.success ? "text-green-500" : "text-red-500"}>●</span>
              <span className="ml-2">Clients API Test</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-2">{testResults.clientsTest.message}</p>
            {testResults.clientsTest.data && (
              <div className="mt-4">
                <p className="font-semibold">Clients Count: {testResults.clientsTest.data.length}</p>
                <div className="mt-2 max-h-32 overflow-y-auto bg-gray-50 p-2 rounded text-xs">
                  <pre>{JSON.stringify(testResults.clientsTest.data.slice(0, 2), null, 2)}</pre>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Tasks Test Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <span className={testResults.tasksTest.success ? "text-green-500" : "text-red-500"}>●</span>
              <span className="ml-2">Tasks API Test</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-2">{testResults.tasksTest.message}</p>
            {testResults.tasksTest.data && (
              <div className="mt-4">
                <p className="font-semibold">Tasks Count: {testResults.tasksTest.data.length}</p>
                <div className="mt-2 max-h-32 overflow-y-auto bg-gray-50 p-2 rounded text-xs">
                  <pre>{JSON.stringify(testResults.tasksTest.data.slice(0, 2), null, 2)}</pre>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeft, Lightbulb, Save, Download, Check, FileText } from 'lucide-react'
import { ErrorBoundary } from 'react-error-boundary'
import { useCoAgent, useCopilotContext } from '@copilotkit/react-core'
import { useSaveDocumentAction } from '@/lib/actions/document-actions'

import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'

import { DocumentEditor } from '@/components/documents/document-editor'
import { VariableForm } from '@/components/documents/variable-form'
import { SectionSuggestions } from '@/components/documents/section-suggestions'
import { updateDocumentVariables } from '@/lib/api-client'

import {
  DocumentDraftMode,
  DocumentDraftStep,
  createDocumentDraftState,
  getTemplateById,
  getMissingVariables,
  SAMPLE_TEMPLATES
} from '@/lib/documents'
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

// Custom hook for debounced function calls
function useDebounce<T extends (...args: unknown[]) => any>(
  callback: T,
  delay: number
): (...args: Parameters<T>) => void {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  return useCallback((...args: Parameters<T>) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);
}

// Define the structure for the suggestions API response
interface SuggestionApiResponse {
  suggestion?: string;
  error?: string;
}

export default function DocumentDraftPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { authedFetch, isReady } = useAuthenticatedFetch();

  // Get parameters from URL
  const draftMode = searchParams.get('mode') as DocumentDraftMode || DocumentDraftMode.HYBRID
  const templateId = searchParams.get('template') || undefined

  // Document state
  const [title, setTitle] = useState('Untitled Document')
  const [isEditing, setIsEditing] = useState(false)
  const [isSaved, setIsSaved] = useState(false)

  // Variable tracking
  const [variables, setVariables] = useState<Record<string, unknown>>({})
  const [missingVariables, setMissingVariables] = useState<string[]>([])

  // Document content
  const [content, setContent] = useState('')
  const [sections, setSections] = useState<{title: string, content: string, isEditable: boolean}[]>([])
  const [currentSection, setCurrentSection] = useState<string | null>(null)

  // CoAgent integration
  const { state: agentState } = useCoAgent({
    name: "document_agent"
  })

  // Load template and set up initial state
  useEffect(() => {
    if (templateId) {
      const template = getTemplateById(templateId)
      if (template) {
        setTitle(`${template.name} - Draft`)

        // Set up initial sections based on template
        const templateSections = template.sections.map(section => ({
          title: section.title,
          content: section.content,
          isEditable: true
        }))
        setSections(templateSections)

        // Initialize all template variables for tracking
        // This ensures we always have a record of all variables
        const allVariableNames = template.variables.map(v => v.name);
        setMissingVariables(allVariableNames);

        // Check for missing required variables
        const missing = getMissingVariables(template, variables)
        setMissingVariables(missing)
      }
    }
  }, [templateId])

  // Add state for tracking update status
  const [variableUpdateStatus, setVariableUpdateStatus] = useState<{
    variable: string;
    status: 'idle' | 'saving' | 'success' | 'error';
    timestamp: number;
  }>({ variable: '', status: 'idle', timestamp: 0 });

  // Memoize the update callback to prevent creating a new function on each render
  const updateVariableCallback = useCallback((docId: string, variable: string, value: any) => {
    // Show saving status
    setVariableUpdateStatus({
      variable,
      status: 'saving',
      timestamp: Date.now()
    });

    // Use the API client to update variables
    updateDocumentVariables(docId, { [variable]: value })
      .then(response => {
        console.log('Variable updated successfully:', response);
        setVariableUpdateStatus({
          variable,
          status: 'success',
          timestamp: Date.now()
        });

        // Reset status after 3 seconds
        setTimeout(() => {
          setVariableUpdateStatus(prev =>
            prev.variable === variable && prev.status === 'success'
              ? { ...prev, status: 'idle' }
              : prev
          );
        }, 3000);
      })
      .catch(error => {
        console.error('Error updating variable:', error.message);
        // Set error status
        setVariableUpdateStatus({
          variable,
          status: 'error',
          timestamp: Date.now()
        });

        // Reset error status after 5 seconds
        setTimeout(() => {
          setVariableUpdateStatus(prev =>
            prev.variable === variable && prev.status === 'error'
              ? { ...prev, status: 'idle' }
              : prev
          );
        }, 5000);

        // Keep the variable in local state, but don&apos;t block the UI
        // This allows the user to continue editing even if the save failed
      });
  }, []);

  // Debounce API calls for variable updates
  const debouncedUpdateVariable = useDebounce(updateVariableCallback, 1000); // 1 second debounce for API calls

  /**
   * Handle variable submission from the input form
   */
  const handleVariableSubmit = (variable: string, value: any) => {
    // Don't process empty values
    if (value === '') return;

    // Avoid unnecessary updates if value hasn't changed
    if (variables[variable] === value) return;

    // Batch the update to improve performance and reduce re-renders
    const updatedVariables = { ...variables, [variable]: value };

    // Update variables in local state immediately
    setVariables(updatedVariables);

    // Update missing variables calculation
    if (templateId) {
      const template = getTemplateById(templateId);
      if (template) {
        // Get only the required missing variables
        const missingRequired = template.variables
          .filter(v => v.required && !updatedVariables[v.name])
          .map(v => v.name);

        // Get all variable names for tracking
        const allVariableNames = template.variables.map(v => v.name);

        // Keep all variables in the list, but mark only the required missing ones
        // This ensures the form continues to show all variables
        setMissingVariables(allVariableNames.filter(name =>
          missingRequired.includes(name) || !updatedVariables[name]
        ));
      }

      // For now, use templateId as document_id (this should be fixed with proper document creation)
      debouncedUpdateVariable(templateId, variable, value);
    }
  }

  // Handler for section editing
  const handleSectionUpdate = (sectionTitle: string, newContent: string) => {
    setSections(prev =>
      prev.map(section =>
        section.title === sectionTitle
          ? { ...section, content: newContent }
          : section
      )
    )
  }

  // Manual save function (extracted from useCopilotAction)
  const handleManualSave = async () => {
    // In a real app, we would save to Supabase
    setIsSaved(true)
    setTimeout(() => setIsSaved(false), 2000)
    console.log("Document saved manually")
    // Note: Return value isn't directly used here like in the action handler
  }

  // Register the save document action with CopilotKit
  useSaveDocumentAction()
  
  // Handler to save the document when the save button is clicked
  const handleSaveDocument = async () => {
    try {
      // In a real app, this would trigger the action
      // The action is available to the AI but we handle manual saves here
      setIsSaved(true)
      setTimeout(() => setIsSaved(false), 2000)
      
      // In production, this would call the same function that the AI uses
      // await saveDocumentData({ title, content: editor?.getHTML() || '', ... })
      console.log("Document saved manually")
    } catch (error) {
      console.error("Error saving document:", error)
    }
  }

  // Error handler
  const handleError = (error: Error) => {
    console.error('CopilotKit Error:', error)
  }

  // Function to calculate progress percentage consistently
  const calculateProgressPercentage = () => {
    if (!templateId) return 0;

    const template = getTemplateById(templateId);
    if (!template) return 0;

    // Count total required variables
    const totalRequired = template.variables.filter(v => v.required).length;
    if (totalRequired === 0) return 100; // If no required fields, progress is 100%

    // Count completed required variables
    const completedRequired = template.variables
      .filter(v => v.required && variables[v.name] && variables[v.name] !== '')
      .length;

    // Calculate percentage, round to nearest integer
    return Math.round((completedRequired / totalRequired) * 100);
  };

  // State for active suggestion and is suggesting
  const [activeSuggestion, setActiveSuggestion] = useState<{ sectionTitle: string; suggestion: string } | null>(null);
  const [isSuggesting, setIsSuggesting] = useState(false);

  // Handler for the 'Improve' button (requests suggestion for whole section)
  const handleRequestSuggestion = useCallback(async (sectionTitle: string) => {
    const section = sections.find(s => s.title === sectionTitle);
    if (!section || !authedFetch || !isReady) return;

    setIsSuggesting(true);
    setActiveSuggestion(null); // Clear previous

    try {
      const sectionContent = section.content;
      const data = await authedFetch<SuggestionApiResponse>('/api/suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sectionTitle, sectionContent }),
      });

      if (data?.error) {
        console.error("Error fetching suggestion:", data.error);
        // TODO: Show error to user
      } else if (data?.suggestion) {
        console.log("Received suggestion:", data.suggestion);
        setActiveSuggestion({ sectionTitle, suggestion: data.suggestion });
      } else {
        console.warn("Received empty suggestion response");
      }
    } catch (err) {
      console.error("Failed to fetch suggestion:", err);
      // TODO: Show error to user
    } finally {
      setIsSuggesting(false);
    }
  }, [sections, authedFetch, isReady]);

  // Handler for the shortcut (requests suggestion for selected text)
  const handleRequestSuggestionForSelection = useCallback(async (sectionTitle: string, selectedText: string) => {
    const section = sections.find(s => s.title === sectionTitle);
    if (!section || !selectedText || !authedFetch || !isReady) return;

    setIsSuggesting(true);
    setActiveSuggestion(null); // Clear previous

    try {
      const sectionContent = section.content; // Send full context if needed
      const data = await authedFetch<SuggestionApiResponse>('/api/suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sectionTitle, sectionContent, selectedText }),
      });

      if (data?.error) {
        console.error("Error fetching suggestion for selection:", data.error);
        // TODO: Show error to user
      } else if (data?.suggestion) {
        console.log("Received suggestion for selection:", data.suggestion);
        setActiveSuggestion({ sectionTitle, suggestion: data.suggestion });
      } else {
        console.warn("Received empty suggestion response for selection");
      }
    } catch (err) {
      console.error("Failed to fetch suggestion for selection:", err);
      // TODO: Show error to user
    } finally {
      setIsSuggesting(false);
    }
  }, [sections, authedFetch, isReady]);

  // Function to clear suggestion (can be passed down)
  const clearSuggestion = useCallback(() => {
    setActiveSuggestion(null);
  }, []);

  return (
    <div className="container max-w-7xl mx-auto py-8 px-4">
      <div className="mb-8 flex justify-between items-center">
        <div className="flex items-center">
          <Link href="/documents">
            <Button variant="outline" size="icon" className="mr-4">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>

          {isEditing ? (
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="text-2xl font-bold w-96"
              onBlur={() => setIsEditing(false)}
              autoFocus
            />
          ) : (
            <h1
              className="text-3xl font-bold tracking-tight cursor-pointer hover:underline"
              onClick={() => setIsEditing(true)}
            >
              {title}
            </h1>
          )}
        </div>

        <div className="flex gap-2">
          <Button
            onClick={handleManualSave}
            disabled={isSaved}
          >
            {isSaved ? (
              <>
                <Check className="mr-2 h-4 w-4" />
                Saved
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Draft
              </>
            )}
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Document creation workflow */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Document editor (main area) */}
        <div className="lg:col-span-2 space-y-6">
          {/* Progress indicator */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <CardTitle className="text-sm font-medium">Document Progress</CardTitle>
                <span className="text-xs text-muted-foreground">
                  {calculateProgressPercentage()}% Complete
                </span>
              </div>
            </CardHeader>
            <CardContent className="pb-1">
              <Progress
                value={calculateProgressPercentage()}
                className="h-2 bg-muted"
                indicatorClassName="bg-primary"
              />
            </CardContent>
            <CardFooter className="pt-1">
              <div className="text-xs text-muted-foreground">
                {missingVariables.length > 0
                  ? `Missing ${missingVariables.length} required fields`
                  : "All required fields completed"}
              </div>
            </CardFooter>
          </Card>

          {/* Document content editor */}
          {draftMode === DocumentDraftMode.AI_ASSISTED ? (
            // AI Generated content
            <Card>
              <CardHeader>
                <CardTitle>AI-Generated Document</CardTitle>
                <CardDescription>This document was generated based on your specifications</CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={agentState?.ai_content || "Your document will appear here once generated..."}
                  onChange={(e) => setContent(e.target.value)}
                  className="min-h-[500px] font-mono text-sm"
                />
              </CardContent>
            </Card>
          ) : (
            // Template-based editor with sections
            <DocumentEditor
              sections={sections}
              variables={variables}
              onUpdateSection={handleSectionUpdate}
              onRequestSuggestion={handleRequestSuggestion}
              onRequestSuggestionForSelection={handleRequestSuggestionForSelection}
              activeSuggestion={activeSuggestion}
              isSuggesting={isSuggesting}
              clearSuggestion={clearSuggestion}
            />
          )}
        </div>

        {/* Sidebar - AI Chat + Variables */}
        <div className="space-y-6">
          {/* Variable Form Card - Always displayed */}
          <Card className="col-span-1">
            <CardHeader>
              <CardTitle>Document Variables</CardTitle>
              <CardDescription>
                Fill in the required information for your document
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Progress indicator */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <Label>Document Progress</Label>
                  <span className="text-sm text-muted-foreground">
                    {calculateProgressPercentage()}% Complete
                  </span>
                </div>
                <Progress
                  value={calculateProgressPercentage()}
                  className="h-2 bg-muted"
                  indicatorClassName="bg-primary"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  {missingVariables.length > 0
                    ? `${missingVariables.length} required field${missingVariables.length === 1 ? '' : 's'} remaining`
                    : 'All required fields completed'}
                </p>
              </div>

              {/* Variable form - Always shown */}
              <VariableForm
                variables={variables}
                missingVariables={missingVariables}
                currentVariable={agentState?.currentVariable || null}
                onSubmit={handleVariableSubmit}
                updateStatus={variableUpdateStatus}
              />
            </CardContent>
          </Card>

          {/* AI suggestions - Conditionally shown */}
          {currentSection && (
            <SectionSuggestions
              sectionTitle={currentSection}
              suggestions={agentState?.ai_suggestions || [
                "Add specific details about the extent of injuries",
                "Include reference to relevant case law",
                "Clarify the timeline of events for better causation narrative"
              ]}
              onApplySuggestion={(suggestion) => {
                // In a real app, we would apply the suggestion to the current section
                console.log(`Applying suggestion: ${suggestion}`)
              }}
            />
          )}

          {/* AI Chat Interface */}
          <Card className="overflow-hidden h-[600px] flex flex-col">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Lightbulb className="h-4 w-4 mr-2" />
                Document Assistant
              </CardTitle>
              <CardDescription>
                Get help drafting your legal document
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0 flex-grow">
              <ErrorBoundary fallback={<div>Something went wrong</div>} onError={handleError}>
                {/* Removed CopilotChat */}
              </ErrorBoundary>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

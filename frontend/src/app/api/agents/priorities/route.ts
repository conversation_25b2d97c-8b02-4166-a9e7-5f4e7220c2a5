// @ts-nocheck - API route type issues
/**
 * API Route: /api/agents/priorities
 *
 * Provides activity priorities and insights for the supervisor agent.
 * This endpoint allows the supervisor agent to access ML-prioritized activities
 * and their associated insights to make informed decisions.
 */
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { z } from 'zod';
import { withAuth, AuthUser } from '@/lib/auth/server-exports';
import { SupabaseClient } from '@supabase/supabase-js';
import { PriorityLevel } from '@/lib/ml/modelInference';

// Environment variables validation
const envSchema = z.object({
  NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1),
});

// Validate environment variables
const env = envSchema.safeParse({
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
  NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
});

if (!env.success) {
  console.error('Environment validation failed:', env.error);
}

// Initialize Supabase client with fallback for build time
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co',
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-anon-key'
);

/**
 * GET handler for retrieving prioritized activities and insights
 *
 * Query parameters:
 * - limit: Maximum number of activities to return (default: 10)
 * - priority: Filter by priority level (high, medium, low)
 * - type: Filter by activity type
 * - target_id: Filter by target ID (e.g., case ID, document ID)
 * - target_type: Filter by target type (e.g., case, document)
 * - with_insights: Include associated insights (default: true)
 */
async function getHandler(_req: NextRequest, user: AuthUser, supabase: SupabaseClient) {
  try {
    const { searchParams } = new URL(req.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const priority = searchParams.get('priority') as PriorityLevel | null;
    const type = searchParams.get('type');
    const targetId = searchParams.get('target_id');
    const targetType = searchParams.get('target_type');
    const withInsights = searchParams.get('with_insights') !== 'false';

    // Start building the query for activities
    let query = supabase
      .from('activities')
      .select('*')
      .eq('tenant_id', user.tenantId)
      .not('metadata->priority_processed', 'is', null)
      .order('metadata->priority_score', { ascending: false });

    // Apply filters if provided
    if (priority) {
      query = query.eq('metadata->priority_level', priority);
    }

    if (type) {
      query = query.eq('type', type);
    }

    if (targetId) {
      query = query.eq('target_id', targetId);
    }

    if (targetType) {
      query = query.eq('target_type', targetType);
    }

    // Execute the query with limit
    const { data: activities, error } = await query.limit(limit);

    if (error) {
      console.error('Error fetching prioritized activities:', error);
      return NextResponse.json({ error: 'Failed to fetch activities' }, { status: 500 });
    }

    // If insights are requested, fetch them for the activities
    let insights = [];
    if (withInsights && activities.length > 0) {
      const activityIds = activities.map((a: any) => a.id);

      const { data: insightsData, error: insightsError } = await supabase
        .from('insights')
        .select('*')
        .eq('tenant_id', user.tenantId)
        .contains('source_activities', activityIds)
        .order('created_at', { ascending: false });

      if (insightsError) {
        console.error('Error fetching insights:', insightsError);
      } else {
        insights = insightsData;
      }
    }

    // Format the response with activities and their associated insights
    const formattedActivities = activities.map((activity: any) => {
      const activityInsights = insights.filter((insight: any) =>
        insight.source_activities.includes(activity.id)
      );

      return {
        ...activity,
        insights: activityInsights
      };
    });

    return NextResponse.json({
      activities: formattedActivities,
      count: formattedActivities.length
    });
  } catch (error: any) {
    console.error('Error in priorities API:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * POST handler for updating activity priorities
 *
 * Request body:
 * {
 *   activity_id: string,
 *   priority_level: "high" | "medium" | "low",
 *   priority_score?: number,
 *   priority_reasoning?: string
 * }
 */
async function postHandler(_req: NextRequest, user: AuthUser, supabase: SupabaseClient) {
  try {
    const body = await req.json();

    // Validate request body
    const { activity_id, priority_level, priority_score, priority_reasoning } = body;

    if (!activity_id || !priority_level) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Get the current activity to update its metadata
    const { data: activity, error: getError } = await supabase
      .from('activities')
      .select('*')
      .eq('id', activity_id)
      .eq('tenant_id', user.tenantId)
      .single();

    if (getError || !activity) {
      return NextResponse.json({ error: 'Activity not found' }, { status: 404 });
    }

    // Update the activity with new priority information
    const updatedMetadata = {
      ...activity.metadata,
      priority_level,
      priority_score: priority_score || activity.metadata?.priority_score || 0.5,
      priority_reasoning: priority_reasoning || activity.metadata?.priority_reasoning || '',
      priority_processed: true,
      priority_updated_at: new Date().toISOString(),
      priority_updated_by: 'supervisor_agent'
    };

    const { error: updateError } = await supabase
      .from('activities')
      .update({ metadata: updatedMetadata })
      .eq('id', activity_id)
      .eq('tenant_id', user.tenantId);

    if (updateError) {
      return NextResponse.json({ error: 'Failed to update activity priority' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: `Updated priority for activity ${activity_id} to ${priority_level}`
    });
  } catch (error: any) {
    console.error('Error in priorities API:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

// Export the route handlers with authentication
export const GET = withAuth(getHandler, ['partner', 'attorney', 'paralegal', 'staff']);
export const POST = withAuth(postHandler, ['partner', 'attorney', 'paralegal', 'staff']);

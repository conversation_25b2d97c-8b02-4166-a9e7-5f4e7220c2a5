// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server'
import { createServerClientForUser } from '@/lib/auth'

// Define interfaces for login request and response
interface LoginRequest {
  email: string;
  password: string;
}

/**
 * User login endpoint
 */
export async function POST(_req: NextRequest): Promise<Response> {
  const supabase = createServerClientForUser(req)
  try {
    const { email, password } = await req.json() as LoginRequest

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Missing email or password' },
        { status: 400 }
      )
    }

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      console.error('Login error:', error)
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json({ session: data.session })
  } catch (err: any) {
    console.error('Unexpected login error:', err)
    return NextResponse.json(
      { error: 'Server error' },
      { status: 500 }
    )
  }
}

// This route is obsolete. Use Supabase official MFA API instead.
import { type NextRequest, NextResponse } from 'next/server';

export const GET = async (_req: NextRequest) => {
  return NextResponse.json(
    {
      error: 'This MFA endpoint is obsolete. Use Supabase official MFA API instead.',
      documentation: 'https://supabase.com/docs/guides/auth/auth-mfa'
    },
    { status: 410 } // 410 Gone
  );
};

export const POST = async (_req: NextRequest) => {
  return NextResponse.json(
    {
      error: 'This MFA endpoint is obsolete. Use Supabase official MFA API instead.',
      documentation: 'https://supabase.com/docs/guides/auth/auth-mfa'
    },
    { status: 410 } // 410 Gone
  );
};

// @ts-nocheck - API route type issues
import { CopilotBackend } from "@copilotkit/backend";
import { NextRequest, NextResponse } from "next/server";

export const runtime = "edge";

// Create a backend instance with the OpenAI API key
// @ts-expect-error
const backend = new CopilotBackend({
  openAIApiKey: process.env.OPENAI_API_KEY!,
} as any);

export async function POST(_req: NextRequest): Promise<Response> {
  try {
    // @ts-expect-error - Ignoring type errors for now as the CopilotBackend API might have changed
    return await backend.chat(req);
  } catch (error: any) {
    console.error('Error in chat API:', error);
    return NextResponse.json({ error: 'Chat processing error' }, { status: 500 });
  }
}

// @ts-nocheck - API route type issues
/**
 * Background job to process activities using ML prioritization
 * This endpoint is designed to be triggered by a cron job
 */
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { extractActivityFeatures, Activity } from '@/lib/ml/features';
import { activityPriorityService, PriorityLevel } from '@/lib/services/activity-priority-service';
import { z } from 'zod';

// Environment variables validation
const envSchema = z.object({
  NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1),
  CRON_SECRET: z.string().min(1).optional(),
});

// Validate environment variables with fallbacks for build time
const env = envSchema.safeParse({
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co',
  NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-anon-key',
  CRON_SECRET: process.env.CRON_SECRET || 'placeholder-cron-secret',
});

if (!env.success) {
  console.error('Environment validation failed:', env.error);
}

// Supabase client (using anon key as this is a background job) with fallback for build time
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co',
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-anon-key'
);

// Process activities for a specific tenant
async function processActivitiesForTenant(tenantId: string) {
  try {
    console.log(`Processing activities for tenant: ${tenantId}`);

    // Get recent unprocessed activities (last 24 hours or without priority)
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    const { data: activities, error } = await supabase
      .from('activities')
      .select('*')
      .eq('tenant_id', tenantId)
      .or(`created_at.gte.${yesterday.toISOString()},metadata->priority_processed.is.null`)
      .order('created_at', { ascending: false })
      .limit(200); // Process in batches

    if (error) {
      throw new Error(`Error fetching activities: ${error.message}`);
    }

    if (!activities || activities.length === 0) {
      console.log(`No activities to process for tenant: ${tenantId}`);
      return 0;
    }

    console.log(`Processing ${activities.length} activities`);
    let processedCount = 0;

    // Process each activity
    for (const activity of activities) {
      try {
        // Extract features
        const features = await extractActivityFeatures(activity as Activity);

        // Get priority prediction using our new service
        const priorityLevel = await activityPriorityService.predictPriority(activity as Activity);
        const priorityScore = activityPriorityService.getPriorityScore(priorityLevel);

        // Update activity with priority information
        const { error: updateError } = await supabase
          .from('activities')
          .update({
            metadata: {
              ...activity.metadata,
              priority_level: priorityLevel,
              priority_score: priorityScore,
              priority_processed: true,
              priority_processed_at: new Date().toISOString(),
              features: features, // Store features for later analysis
            },
          })
          .eq('id', activity.id)
          .eq('tenant_id', tenantId);

        if (updateError) {
          console.error(`Error updating activity ${activity.id}:`, updateError);
          continue;
        }

        processedCount++;
      } catch (activityError) {
        console.error(`Error processing activity ${activity.id}:`, activityError);
      }
    }

    console.log(`Successfully processed ${processedCount} activities for tenant: ${tenantId}`);
    return processedCount;
  } catch (error) {
    console.error(`Error in processActivitiesForTenant for ${tenantId}:`, error);
    return 0;
  }
}

// Main handler for the API route
export async function GET(_req: NextRequest) {
  // Verify cron secret for security
  const authHeader = req.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ') || authHeader.split(' ')[1] !== process.env.CRON_SECRET) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Handle tenant-specific processing if specified
    const tenantId = req.nextUrl.searchParams.get('tenantId');

    if (tenantId) {
      // Process activities for specific tenant
      const processedCount = await processActivitiesForTenant(tenantId);
      return NextResponse.json({
        success: true,
        message: `Processed ${processedCount} activities for tenant ${tenantId}`,
      });
    } else {
      // Process for all active tenants
      const { data: tenants, error: tenantError } = await supabase
        .from('firms')
        .select('tenant_id')
        .eq('status', 'active');

      if (tenantError) {
        throw new Error(`Error fetching tenants: ${tenantError.message}`);
      }

      if (!tenants || tenants.length === 0) {
        return NextResponse.json({ message: 'No active tenants found' });
      }

      // Process each tenant
      const results = [];
      for (const tenant of tenants) {
        const processedCount = await processActivitiesForTenant(tenant.tenant_id);
        results.push({
          tenantId: tenant.tenant_id,
          processed: processedCount,
        });
      }

      return NextResponse.json({
        success: true,
        message: `Processed activities for ${results.length} tenants`,
        results,
      });
    }
  } catch (error: unknown) {
    console.error('Error processing activities:', error);
    let errorMessage = 'Failed to process activities';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    return NextResponse.json(
      { error: 'Cron job failed', details: errorMessage },
      { status: 500 }
    );
  }
}

// For serverless function warmup
export async function HEAD() {
  return new NextResponse(null, { status: 200 });
}

// @ts-nocheck - API route type issues
import { withAuth, UserRole } from '@/lib/auth/server-exports';
import { NextRequest, NextResponse } from 'next/server';
import type { AuthUser } from '@/lib/auth/server-exports';
import type { SupabaseClient } from '@supabase/supabase-js';
import { createServices } from '@/lib/services';
import { Database } from '@/lib/database.types';

// Define interfaces for analysis data
interface TaskData {
  title: string;
  description: string;
  deadline?: string;
  responsible_party?: string;
  confidence_score?: number;
  priority?: string;
}

interface AnalysisResult {
  success: boolean;
  result: TaskData[] | Record<string, unknown>;
  error?: string;
}

export const POST = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (
    req: NextRequest,
    user: AuthUser,
    supabase: SupabaseClient<Database, "public", any>,
    context: Record<string, unknown>
  ): Promise<Response> => {
  try {
    const formData = await req.formData();

    // Forward the request to the Python API
    const response = await fetch(`${process.env.LANGGRAPH_API_URL || 'http://localhost:8000'}/document-analysis/analyze`, {
      method: 'POST',
      body: formData, // Pass along the form data with the file
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Document analysis failed: ${response.statusText}. Details: ${errorText}`);
    }

    const analysisResult = await response.json() as AnalysisResult;

    // If tasks were identified and analysis type was 'tasks', create them
    if (analysisResult.success && formData.get('analysis_type') === 'tasks' &&
        Array.isArray(analysisResult.result)) {

      const services = createServices(supabase, user.tenantId);
      const documentId = formData.get('document_id')?.toString();
      const caseId = formData.get('case_id')?.toString();

      // Create tasks from the analysis results
      for (const taskData of analysisResult.result) {
        await services.tasks.create(user.id, {
          title: taskData.title,
          description: taskData.description,
          due_date: taskData.deadline ? new Date(taskData.deadline).toISOString() : undefined,
          status: 'todo',
          related_case: caseId,
          assigned_to: taskData.responsible_party || user.id,
          ai_metadata: {
            source: 'gemini_document_analysis',
            document_id: documentId,
            confidence_score: taskData.confidence_score || 0.8,
            priority: taskData.priority || 'medium'
          }
        });
      }
    }

    // For medical form analysis, store the structured data
    if (analysisResult.success && formData.get('analysis_type') === 'medical' &&
        typeof analysisResult.result === 'object') {

      const documentId = formData.get('document_id')?.toString();

      if (documentId) {
        // Store the analysis result in the authored_document_analyses table
        await supabase
          .schema('tenants')
          .from('authored_document_analyses')
          .insert({
            document_id: documentId,
            tenant_id: user.tenantId,
            analysis_type: 'medical',
            result: analysisResult.result,
            created_by: user.id,
            created_at: new Date().toISOString()
          });
      }
    }

    return NextResponse.json({ success: true, analysis: analysisResult });
  } catch (error: unknown) {
    console.error('Document analysis error:', error);
    let errorMessage = 'Failed to analyze document';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
    return NextResponse.json(
      { error: 'Document analysis failed', details: errorMessage },
      { status: 500 }
    );
  }
}, ['partner', 'attorney', 'paralegal', 'staff']);

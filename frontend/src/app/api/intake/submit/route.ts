// @ts-nocheck - API route type issues
// frontend/src/app/api/intake/submit/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthUser } from '@/lib/auth/server-exports';
import { createServices } from '@/lib/services';
import type { SupabaseClient } from '@supabase/supabase-js';
import { z } from 'zod';

// Define the schema for intake data validation
const IntakeSchema = z.object({
  first_name: z.string().min(1, 'First name is required'),
  middle_name: z.string().optional(),
  last_name: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address').optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip_code: z.string().optional(),
  occupation: z.string().optional(),
  employer: z.string().optional(),
  work_status: z.string().optional(),
  practice_area: z.string(),
  case_type: z.string(),
  case_description: z.string(),
  date_of_incident: z.string().optional(),
  other_party_names: z.string().optional(),
  previous_attorney_consultation: z.boolean().default(false),
  previous_consultation_details: z.string().optional(),
  intake_priority: z.string().default('medium')
});

// POST: Create a new client intake
export const POST = withAuth(async (_req: NextRequest, user: AuthUser, supabase: SupabaseClient, context: Record<string, unknown>): Promise<Response> => {
  try {
    const body = await req.json();

    // Validate the request body against our schema
    const validatedData = IntakeSchema.parse(body);

    // Create services for the current user's tenant
    const services = createServices(supabase, user.tenantId);

    // Format the data for the database
    const clientData = {
      first_name: validatedData.first_name,
      last_name: validatedData.last_name,
      email: validatedData.email,
      phone_primary: validatedData.phone,
      address: validatedData.address ? {
        street: validatedData.address,
        city: validatedData.city,
        state: validatedData.state,
        zip: validatedData.zip_code
      } : undefined,
      client_type: 'individual' as const, // Default for intake
      intake_date: new Date().toISOString().split('T')[0],
      status: 'pending' as const
    };

    // Create the client
    const clientResult = await services.clients.create(clientData, user.id);

    if (!clientResult.success) {
      return NextResponse.json(
        { error: 'Failed to create client', details: clientResult.error },
        { status: 500 }
      );
    }

    // Format the intake data
    const intakeData = {
      client_id: clientResult.data.id,
      practice_area: validatedData.practice_area,
      case_type: validatedData.case_type,
      case_description: validatedData.case_description,
      date_of_incident: validatedData.date_of_incident,
      other_party_names: validatedData.other_party_names,
      previous_attorney_consultation: validatedData.previous_attorney_consultation,
      previous_consultation_details: validatedData.previous_consultation_details,
      priority: validatedData.intake_priority,
      occupation: validatedData.occupation,
      employer: validatedData.employer,
      work_status: validatedData.work_status,
      status: 'new'
    };

    // Create the intake
    const intakeResult = await services.intakes.create(intakeData);

    if (!intakeResult.success) {
      return NextResponse.json(
        { error: 'Failed to create intake', details: intakeResult.error },
        { status: 500 }
      );
    }

    // Return success with client and intake IDs
    return NextResponse.json({
      success: true,
      client_id: clientResult.data.id,
      intake_id: intakeResult.data.id
    });

  } catch (error) {
    console.error('Error creating intake:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to process intake submission' },
      { status: 500 }
    );
  }
});

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';
import { UsageTrackingService } from '@/lib/services/usage-tracking-service';
import { NotificationService } from '@/lib/services/notification-service';
import { NotificationSchedulerService } from '@/lib/services/notification-scheduler-service';
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client';

// Create a Supabase client for the middleware
const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

// Enhance the client with schema support
const enhancedClient = enhanceClientWithSchemas(supabase);

/**
 * Middleware to track API usage and enforce quota limits
 * @param req The Next.js request object
 * @param handler The API route handler
 * @param usageType The type of usage to track
 * @param incrementBy The amount to increment the usage by
 * @param resourceSizeBytes The size of the resource in bytes (optional)
 * @returns The API route handler response
 */
export async function withUsageTracking(
  req: NextRequest,
  handler: (_req: NextRequest) => Promise<NextResponse>,
  usageType: string,
  incrementBy: number = 1,
  resourceSizeBytes?: number
) {
  // Get user session
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }

  // Get tenant ID from JWT claims
  const tenantId = session.user.app_metadata.tenant_id;

  if (!tenantId) {
    return NextResponse.json(
      { error: 'Tenant ID not found in user claims' },
      { status: 403 }
    );
  }

  // Check quota limit
  const usageTrackingService = new UsageTrackingService(supabase);
  const quotaCheck = await usageTrackingService.checkQuotaLimit({
    tenantId,
    usageType,
    incrementBy,
    resourceSizeBytes,
  });

  if (!quotaCheck.withinQuota) {
    // Send quota exceeded notification
    const notificationService = new NotificationService(supabase);
    const notificationSchedulerService = new NotificationSchedulerService(supabase);

    // Get tenant admin users
    const { data: adminUsers, error: adminError } = await enhancedClient.tenants
      .from('users')
      .select('id, email')
      .eq('tenant_id', tenantId)
      .eq('role', 'admin');

    if (!adminError && adminUsers) {
      // Send notifications to all admin users
      for (const user of adminUsers as any[]) {
        await notificationService.createNotification(
          tenantId,
          user.id,
          'quota_limit_reached',
          `You have reached your ${usageType} quota limit. Please upgrade your plan to continue.`,
          'high',
          {
            usageType,
            currentUsage: quotaCheck.currentUsage,
            quotaLimit: quotaCheck.quotaLimit,
            percentUsed: quotaCheck.percentUsed,
          }
        );

        // Schedule email notification
        await notificationSchedulerService.scheduleNotification(
          tenantId,
          user.id,
          'quota_limit_reached',
          new Date(), // Send immediately
          {
            quotaType: usageType,
            email: user.email,
          }
        );
      }
    }

    return NextResponse.json(
      {
        error: 'Quota exceeded',
        details: {
          usageType,
          currentUsage: quotaCheck.currentUsage,
          quotaLimit: quotaCheck.quotaLimit,
          percentUsed: quotaCheck.percentUsed
        }
      },
      { status: 403 }
    );
  }

  // If quota is approaching limit (80% or more), send a notification
  if (quotaCheck.percentUsed >= 80) {
    // Send quota approaching notification
    const notificationService = new NotificationService(supabase);
    const notificationSchedulerService = new NotificationSchedulerService(supabase);

    // Get tenant admin users
    const { data: adminUsers, error: adminError } = await enhancedClient.tenants
      .from('users')
      .select('id, email')
      .eq('tenant_id', tenantId)
      .eq('role', 'admin');

    if (!adminError && adminUsers) {
      // Send notifications to all admin users
      for (const user of adminUsers as any[]) {
        await notificationService.createNotification(
          tenantId,
          user.id,
          'quota_limit_approaching',
          `You have used ${quotaCheck.percentUsed}% of your ${usageType} quota.`,
          'medium',
          {
            usageType,
            currentUsage: quotaCheck.currentUsage,
            quotaLimit: quotaCheck.quotaLimit,
            percentUsed: quotaCheck.percentUsed,
          }
        );

        // Schedule email notification
        await notificationSchedulerService.scheduleNotification(
          tenantId,
          user.id,
          'quota_limit_approaching',
          new Date(), // Send immediately
          {
            quotaType: usageType,
            percentUsed: quotaCheck.percentUsed,
            email: user.email,
          }
        );
      }
    }
  }

  // Track resource usage
  await usageTrackingService.incrementResourceUsage({
    tenantId,
    usageType,
    incrementBy,
    resourceSizeBytes,
  });

  // Call the handler
  return handler(req);
}

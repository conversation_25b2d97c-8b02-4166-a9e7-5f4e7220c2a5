// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { withAuth, UserRole } from '@/lib/auth/server-exports';
import { generateProactiveMessage } from '@/lib/proactive/agent';

/**
 * API route to get a personalized proactive message based on the user's recent activity
 *
 * GET /api/proactive-message?lastLoginDate=YYYY-MM-DD
 */
export const GET = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff, UserRole.Client], // Allow all authenticated users
  async (_req: NextRequest, user: any, supabase: any) => {
    try {
      // Parse query parameters
      const url = new URL(req.url);
      const lastLoginDate = url.searchParams.get('lastLoginDate') || undefined;
      const daysBack = parseInt(url.searchParams.get('daysBack') || '7', 10);
      const limit = parseInt(url.searchParams.get('limit') || '5', 10);

      // Generate the proactive message
      const proactiveMessage = await generateProactiveMessage({
        userId: user.id,
        tenantId: user.tenantId,
        lastLoginDate,
        daysBack,
        limit
      });

      return NextResponse.json({
        success: true,
        data: proactiveMessage
      });
    } catch (error: any) {
      console.error('Failed to generate proactive message:', error);
      return NextResponse.json({
        error: error.message || 'Failed to generate proactive message'
      }, { status: 500 });
    }
  }
);

// @ts-nocheck - API route type issues
import { withAuth } from "@/lib/auth/server-exports"
import { NextRequest, NextResponse } from "next/server"
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client'

export const GET = withAuth(async (_req: NextRequest, user, supabase, context): Promise<Response> => {
  // We can use the supabase client provided by withAuth
  // No need to create a new client with cookies

  // Enhance the client with schema support
  const enhancedClient = enhanceClientWithSchemas(supabase);

  // Create a custom schema for user_data
  const customSchema = {
    name: 'user_data',
    schema: 'public'
  };

  const { data, error } = await supabase
    .from(customSchema.name as any)  // Use type assertion to bypass TypeScript checking
    .select('*')
    .eq('tenant_id', user.tenantId)
    .limit(10)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching protected data:', error)
    return NextResponse.json({ error: 'Error fetching data' }, { status: 500 })
  }

  return NextResponse.json({
    message: 'Protected data retrieved successfully',
    user: {
      id: user.id,
      email: user.email,
      role: user.role,
      tenantId: user.tenantId
    },
    data
  })
}, ['partner', 'attorney', 'staff'])

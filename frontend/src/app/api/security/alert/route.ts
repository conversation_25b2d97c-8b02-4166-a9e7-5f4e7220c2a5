// @ts-nocheck - API route type issues
import { withAuth } from '@/lib/auth/server-exports'
import { createServiceClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client'

export const POST = withAuth(async (_req: NextRequest, user) => {
  // Create a Supabase client with service role
  const supabase = createServiceClient()

  // Enhance the client with schema support
  const enhancedClient = enhanceClientWithSchemas(supabase)

  try {
    const { eventType, details } = await req.json()

    // Validate required fields
    if (!eventType || !details) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Log the alert in our database
    // Use a type assertion to bypass TypeScript checking for RPC functions
    const { data, error } = await supabase.rpc(
      'log_security_event' as any,
      {
        p_event_type: `external_alert.${eventType}`,
        p_details: {
          ...details,
          alert_sent: true
        }
      }
    )

    if (error) {
      console.error('Error logging alert event:', error)
      return NextResponse.json(
        { error: 'Error logging alert event' },
        { status: 500 }
      )
    }

    // Send to external system (e.g., Slack)
    // This is just an example - replace with your actual integration
    if (process.env.SLACK_WEBHOOK_URL) {
      try {
        const slackResponse = await fetch(process.env.SLACK_WEBHOOK_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            text: `🚨 Security Alert: ${eventType}`,
            blocks: [
              {
                type: 'header',
                text: {
                  type: 'plain_text',
                  text: `🚨 Security Alert: ${eventType}`
                }
              },
              {
                type: 'section',
                text: {
                  type: 'mrkdwn',
                  text: `*Details:*\n${JSON.stringify(details, null, 2)}`
                }
              },
              {
                type: 'context',
                elements: [
                  {
                    type: 'mrkdwn',
                    text: `*User:* ${user.email} | *Event ID:* ${data}`
                  }
                ]
              }
            ]
          })
        })

        if (!slackResponse.ok) {
          throw new Error(`Slack API error: ${slackResponse.statusText}`)
        }
      } catch (slackError) {
        console.error('Error sending Slack notification:', slackError)
        // We still return success to the client even if Slack notification fails
      }
    }

    // Send email alert for high-priority events
    if (eventType.includes('suspicious') || eventType.includes('breach')) {
      // Implement email sending logic here
      // This would typically use a service like SendGrid, Mailgun, etc.
    }

    return NextResponse.json({ success: true, eventId: data })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Server error' },
      { status: 500 }
    )
  }
}, ['partner', 'attorney'])

// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { getGeolocationData } from '@/lib/security/geolocation';
import { analyzeSecurityEvent, logAnomalyEvent } from '@/lib/security/anomaly-detection';
import { trackTokenUsage } from '@/lib/security/token-tracking';
import { sendAnomalyAlert } from '@/lib/security/alerting';

/**
 * Security event categories
 */
export enum SecurityEventCategory {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  DATA_ACCESS = 'data_access',
  SUSPICIOUS = 'suspicious',
  SYSTEM = 'system',
  OTHER = 'other'
}

/**
 * Map event types to categories
 */
const eventCategoryMap: Record<string, SecurityEventCategory> = {
  'auth.login': SecurityEventCategory.AUTHENTICATION,
  'auth.logout': SecurityEventCategory.AUTHENTICATION,
  'auth.password_reset': SecurityEventCategory.AUTHENTICATION,
  'auth.test_event': SecurityEventCategory.SYSTEM,
  'auth.signup': SecurityEventCategory.AUTHENTICATION,
  'auth.token_refresh': SecurityEventCategory.AUTHENTICATION,
  'auth.token_revoked': SecurityEventCategory.AUTHENTICATION,
  'auth.mfa_enabled': SecurityEventCategory.AUTHENTICATION,
  'auth.mfa_disabled': SecurityEventCategory.AUTHENTICATION,
  'auth.mfa_challenge': SecurityEventCategory.AUTHENTICATION,
  'auth.permission_changed': SecurityEventCategory.AUTHORIZATION,
  'auth.role_assigned': SecurityEventCategory.AUTHORIZATION,
  'auth.role_removed': SecurityEventCategory.AUTHORIZATION,
  'data.sensitive_access': SecurityEventCategory.DATA_ACCESS,
  'data.export': SecurityEventCategory.DATA_ACCESS,
  'data.bulk_operation': SecurityEventCategory.DATA_ACCESS,
  'suspicious.multiple_failures': SecurityEventCategory.SUSPICIOUS,
  'suspicious.unusual_location': SecurityEventCategory.SUSPICIOUS,
  'suspicious.unusual_time': SecurityEventCategory.SUSPICIOUS,
  'device.new': SecurityEventCategory.AUTHENTICATION,
  'device.trusted': SecurityEventCategory.AUTHENTICATION,
  'device.removed': SecurityEventCategory.AUTHENTICATION
};

export async function POST(_req: NextRequest) {
  try {
    // Create a Supabase client with service role
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    // Get the request body
    const { eventType, details } = await req.json();

    // Get IP address and user agent from request headers
    const ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
    const userAgent = req.headers.get('user-agent') || 'unknown';

    // Use the userId from the details directly
    const userId = details.userId || null;

    // Determine event category
    const category = eventCategoryMap[eventType] || SecurityEventCategory.OTHER;

    // Get geolocation data
    const geoData = await getGeolocationData(ip);

    // Insert into the security.events table using the service role
    const { data, error } = await supabase
      .schema('security')
      .from('events')
      .insert({
        event_type: eventType,
        event_category: category,
        user_id: userId,
        ip_address: ip,
        user_agent: userAgent,
        location_city: geoData?.city || null,
        location_region: geoData?.region || null,
        location_country: geoData?.country_name || null,
        location_coordinates: geoData ? [geoData.longitude, geoData.latitude] : null,
        details: {
          ...details,
          geolocation: geoData || null
        },
        created_at: new Date().toISOString()
      })
      .select('id')
      .single();

    if (error) {
      console.error('Error logging security event:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    const eventId = data.id;

    // Track token usage if this is an authentication event and we have a token ID
    if (category === SecurityEventCategory.AUTHENTICATION && details.tokenId) {
      await trackTokenUsage(
        details.tokenId,
        userId || 'anonymous',
        ip,
        userAgent
      );
    }

    // Analyze the event for anomalies if we have a user ID
    if (userId) {
      try {
        const anomalyScore = await analyzeSecurityEvent(
          eventType,
          {
            ...details,
            ip_address: ip,
            user_agent: userAgent,
            geolocation: geoData,
            eventId
          },
          userId
        );

        // If the anomaly score is high enough, log it and send an alert
        if (anomalyScore.score >= 50) {
          await logAnomalyEvent(anomalyScore);
          await sendAnomalyAlert(anomalyScore);
        }
      } catch (anomalyError) {
        console.error('Error analyzing security event:', anomalyError);
        // Continue processing even if anomaly detection fails
      }
    }

    return NextResponse.json({ success: true, data: eventId });
  } catch (err) {
    console.error('Error in security log API:', err);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

/**
 * API endpoint for setting up security tables
 * This endpoint is protected and should only be accessible to administrators
 */
export async function POST(_req: NextRequest) {
  try {
    // Create a Supabase client with service role and fallback for build time
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co',
      process.env.SUPABASE_SERVICE_KEY || 'placeholder-service-key'
    );

    // Execute SQL to create the necessary tables
    const { error } = await supabase.rpc('setup_security_tables');

    if (error) {
      console.error('Error setting up security tables:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (err) {
    console.error('Error in security setup API:', err);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

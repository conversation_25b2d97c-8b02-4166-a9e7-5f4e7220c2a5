// @ts-nocheck - API route type issues
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(_req: NextRequest): Promise<Response> {
  try {
    console.log('Templates Test API - Starting test')

    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            cookieStore.set(name, value, options);
          },
          remove(name: string, options: any) {
            cookieStore.delete(name);
          },
        },
      }
    )

    // Get current user session
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession()

    if (sessionError) {
      console.error('Session error:', sessionError)
      return NextResponse.json({
        status: 'error',
        message: 'Failed to get session',
        error: sessionError
      }, { status: 500 })
    }

    // Get user info
    const { data: userData, error: userError } = await supabase.auth.getUser()

    if (userError) {
      console.error('User error:', userError)
      return NextResponse.json({
        status: 'error',
        message: 'Failed to get user',
        error: userError
      }, { status: 500 })
    }

    // Simple query to test database connection
    const { data, error, count } = await supabase
      .from('legal_templates')
      .select('*', { count: 'exact', head: true })
      .limit(1)
      .single()

    if (error) {
      console.error('Test query error:', error)
      return NextResponse.json({
        status: 'error',
        message: 'Database query failed',
        error: error,
        session: sessionData ? 'Valid' : 'Invalid',
        user: userData?.user ? {
          id: userData.user.id,
          email: userData.user.email
        } : null
      }, { status: 500 })
    }

    return NextResponse.json({
      status: 'success',
      message: 'Test successful',
      count: count || 0,
      session: sessionData.session ? {
        expires_at: sessionData.session.expires_at,
        user_id: sessionData.session.user.id
      } : null,
      user: userData?.user ? {
        id: userData.user.id,
        email: userData.user.email
      } : null
    })
  } catch (err) {
    console.error('Test endpoint error:', err)
    return NextResponse.json({
      status: 'error',
      message: 'Unexpected error in test endpoint',
      details: err instanceof Error ? err.message : String(err)
    }, { status: 500 })
  }
}

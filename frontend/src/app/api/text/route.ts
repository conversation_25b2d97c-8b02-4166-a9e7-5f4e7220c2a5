// @ts-nocheck - API route type issues
import { CopilotBackend } from "@copilotkit/backend";
import { NextRequest } from "next/server";

export const runtime = "edge";

const backend = new CopilotBackend({
  // @ts-expect-error - Copilotkit backend config
openAIApiKey: process.env.OPENAI_API_KEY!,
});

export async function POST(_req: NextRequest) {
  // @ts-expect-error - Copilotkit backend method
return backend.text(req);
}

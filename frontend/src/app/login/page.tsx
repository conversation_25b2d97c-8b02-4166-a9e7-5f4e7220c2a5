// frontend/src/app/login/page.tsx
"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"

import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { loginSchema } from "@/lib/schemas"
import { useRouter } from "next/navigation"
import { useSearchParams } from "next/navigation"
import { Turnstile } from "@marsidev/react-turnstile"
import { useSupabase } from "@/lib/supabase/provider"

type FormData = z.infer<typeof loginSchema>
const mfaSchema = z.object({
  mfaCode: z.string().length(6, { message: "MFA code must be 6 digits." }),
});
type MfaFormData = z.infer<typeof mfaSchema>;

export default function LoginPage() {
  const form = useForm<FormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  })

  const mfaForm = useForm<MfaFormData>({
    resolver: zodResolver(mfaSchema),
    defaultValues: {
      mfaCode: "",
    },
  });

  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showMfaInput, setShowMfaInput] = useState(false)
  const [factorId, setFactorId] = useState<string | null>(null)
  const [challengeId, setChallengeId] = useState<string | null>(null)
  const [captchaToken, setCaptchaToken] = useState<string | null>(null)
  const router = useRouter()
  const searchParams = useSearchParams()
  const isClientLogin = searchParams.get('type') === 'client'
  const { supabase } = useSupabase()

  // Check if we&apos;re in a preview environment where Turnstile might not work
  const isPreviewEnvironment = typeof window !== 'undefined' &&
    (window.location.hostname.includes('vercel.app') ||
     window.location.hostname.includes('localhost') ||
     window.location.hostname.includes('127.0.0.1'))

  // For preview environments, auto-set a test token if Turnstile site key is not properly configured
  const shouldUseTurnstile = process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY &&
    process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY !== 'your_turnstile_site_key'

  useEffect(() => {
    console.log('Login Page Mounted:', { isClientLogin, shouldUseTurnstile, isPreviewEnvironment })

    // Auto-set captcha token for preview environments if Turnstile is not available
    if (isPreviewEnvironment && !shouldUseTurnstile) {
      console.log('Preview environment detected without Turnstile - using test token')
      setCaptchaToken('preview-test-token')
    }
  }, [isClientLogin, shouldUseTurnstile, isPreviewEnvironment])

  async function onSubmit(values: FormData) {
    try {
      console.log('Regular login attempt with email:', values.email);

      if (!captchaToken) {
        console.log('Captcha token missing');
        setError("Please complete the Turnstile verification");
        return;
      }

      console.log('Using captcha token:', captchaToken.substring(0, 20) + '...');

      console.log('Attempting to sign in with Supabase...');
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: values.email,
        password: values.password,
        options: { captchaToken },
      });

      if (signInError) {
        if (signInError.message === 'MFA verification required') {
          console.log('MFA required, proceeding to challenge...');
          const { data: factorsData, error: factorsError } = await supabase.auth.mfa.listFactors();

          if (factorsError || !factorsData || !factorsData.all || factorsData.all.length === 0) {
            setError(factorsError?.message || 'Could not retrieve MFA factors.');
            setLoading(false);
            return;
          }

          const totpFactor = factorsData.all.find(f => f.factor_type === 'totp' && f.status === 'verified');

          if (!totpFactor) {
            setError('No verified TOTP factor found. Please set one up.');
            setLoading(false);
            return;
          }

          const { data: challengeData, error: challengeError } = await supabase.auth.mfa.challenge({ factorId: totpFactor.id });

          if (challengeError) {
            setError(challengeError.message || 'Failed to initiate MFA challenge.');
            setLoading(false);
            return;
          }

          setFactorId(totpFactor.id);
          setChallengeId(challengeData.id);
          setShowMfaInput(true);

        } else {
          setError(signInError.message);
        }
        setLoading(false);
        return;
      }

      if (signInData.session) {
        console.log('Sign in successful (no MFA needed this time)');
        const userRole = signInData.user.user_metadata.role;
        const tenantId = signInData.user.user_metadata.tenant_id || signInData.user.app_metadata.tenant_id;
        if (!tenantId) throw new Error("No tenant_id found");
        const { error: updateErr } = await supabase.auth.updateUser({
          data: { ...signInData.user.user_metadata, tenant_id: tenantId, role: userRole },
        });
        if (updateErr) throw updateErr;
        if (isClientLogin && userRole !== "client") {
          await supabase.auth.signOut();
          setError("Invalid client credentials");
          return;
        } else if (!isClientLogin && userRole === "client") {
          await supabase.auth.signOut();
          setError("Please use the client portal to login");
          return;
        }
        const redirectPath = userRole === "client" ? "/client-portal" : "/dashboard";
        console.log('Authentication successful, redirecting to:', redirectPath);
        console.log('User data:', { id: signInData.user.id, email: signInData.user.email, role: userRole, tenantId });

        router.push(redirectPath);
        setTimeout(() => {
          if (window.location.pathname === '/login') {
            console.log('Router redirect failed, trying direct location change');
            window.location.href = redirectPath;
          }
        }, 500);
      } else {
         setError('Sign in completed, but no session received.');
         setLoading(false);
      }

    } catch (err: unknown) {
      console.error('Login catch block:', err)
      setError(err instanceof Error ? err.message : 'An unexpected error occurred.')
    }
  }

  const handleMfaSubmit = async (values: MfaFormData) => {
    if (!factorId || !challengeId) {
      setError('Internal error: Missing MFA details.');
      return;
    }
    setLoading(true);
    setError(null);

    try {
      const { data: verifyData, error: verifyError } = await supabase.auth.mfa.verify({
        factorId,
        challengeId,
        code: values.mfaCode
      });

      if (verifyError) {
        setError(verifyError.message);
        setLoading(false);
        return;
      }

      console.log('MFA Verification successful');
      console.log('Session User:', verifyData.user);

      const userRole = verifyData.user.user_metadata.role;
      const tenantId = verifyData.user.user_metadata.tenant_id || verifyData.user.app_metadata.tenant_id;
      if (!tenantId) throw new Error("No tenant_id found");
      const { error: updateErr } = await supabase.auth.updateUser({
        data: { ...verifyData.user.user_metadata, tenant_id: tenantId, role: userRole },
      });
      if (updateErr) throw updateErr;
      if (isClientLogin && userRole !== "client") {
        await supabase.auth.signOut();
        setError("Invalid client credentials");
        return;
      } else if (!isClientLogin && userRole === "client") {
        await supabase.auth.signOut();
        setError("Please use the client portal to login");
        return;
      }
      const redirectPath = userRole === "client" ? "/client-portal" : "/dashboard";
      console.log('Authentication successful, redirecting to:', redirectPath);
      console.log('User data:', { id: verifyData.user.id, email: verifyData.user.email, role: userRole, tenantId });

      router.push(redirectPath);
      setTimeout(() => {
        if (window.location.pathname === '/login') {
          console.log('Router redirect failed, trying direct location change');
          window.location.href = redirectPath;
        }
      }, 500);
    } catch (err: unknown) {
      console.error('MFA verification error:', err);
      const message = err instanceof Error ? err.message : 'An unknown error occurred during MFA verification.';
      setError(message);
    } finally {
      setLoading(false)
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center px-4 py-12 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">{isClientLogin ? 'Client Login' : 'Staff Login'}</CardTitle>
          <CardDescription>
            {isClientLogin
              ? 'Login to access your case portal'
              : 'Login to access your dashboard'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Conditionally render MFA form or Login form */}
          {showMfaInput ? (
            <Form {...mfaForm}>
              <form onSubmit={mfaForm.handleSubmit(handleMfaSubmit)} className="space-y-4 mt-4">
                <FormField
                  control={mfaForm.control}
                  name="mfaCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>MFA Code</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          inputMode="numeric"
                          autoComplete="one-time-code"
                          maxLength={6}
                          placeholder="Enter 6-digit code"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* Display MFA specific errors */}
                {error && (
                  <div className="rounded-md bg-red-50 p-4">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                )}
                <Button type="submit" className="w-full" disabled={loading}>
                  {loading ? "Verifying..." : "Verify MFA Code"}
                </Button>
              </form>
            </Form>
          ) : (
            // Render Login Form initially
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                {/* Email Field */}
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* Password Field */}
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input type="password" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* Root form errors (if any) */}
                {form.formState.errors.root && (
                  <div className="text-red-500 text-sm text-center">
                    {form.formState.errors.root.message}
                  </div>
                )}
                {/* Turnstile and General Error Display */}
                <div className="space-y-2">
                  {shouldUseTurnstile ? (
                    <div className="w-full flex justify-center">
                      <Turnstile
                        siteKey={process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY!}
                        onSuccess={(token) => setCaptchaToken(token)}
                        onError={() => {
                          setError("Turnstile verification failed")
                          setCaptchaToken(null)
                        }}
                      />
                    </div>
                  ) : (
                    <div className="w-full flex justify-center">
                      <div className="text-sm text-muted-foreground bg-yellow-50 border border-yellow-200 rounded p-3">
                        ⚠️ Preview Environment: Turnstile disabled for testing
                      </div>
                    </div>
                  )}
                  {/* Display general login errors (not MFA specific) */}
                  {error && !showMfaInput && (
                    <div className="rounded-md bg-red-50 p-4">
                      <p className="text-sm text-red-700">{error}</p>
                    </div>
                  )}
                </div>
                {/* Submit Button */}
                <Button type="submit" className="w-full" disabled={loading || !captchaToken}>
                  {loading ? "Signing In..." : "Sign In"}
                </Button>
              </form>
            </Form>
          )}
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <div className="text-sm text-center">
            Don&apos;t have an account?{" "}
            <Link href="/register" className="font-medium text-primary hover:underline">
              Sign up
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}

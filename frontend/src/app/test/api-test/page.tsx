'use client';

import { useState, useEffect } from 'react';
import { useSupabase } from '@/lib/supabase/provider';
import { useUser } from '@/contexts/UserContext';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { FingerprintJSClientProvider } from '@/components/providers/fpjs-provider';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { CheckCircle2, XCircle, AlertTriangle } from 'lucide-react';

// Helper to parse JWT payload (basic implementation)
const parseJwtPayload = (token: string): any | null => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(function (c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        })
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (e) {
    console.error('Failed to parse JWT payload:', e);
    return null;
  }
};

// Interface for test data
interface TestData {
  TASK_ID: string | null;
  CASE_ID: string | null;
  CLIENT_ID: string | null;
  loaded: boolean;
  usingFallbacks: {
    TASK_ID: boolean;
    CASE_ID: boolean;
    CLIENT_ID: boolean;
  };
}

// Interface for API test definitions
interface TestDefinition {
  name: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'; // Be specific with methods
  url: string;
  description: string;
  expectedRoles: string[];
  body?: any; // Use a more specific type if possible
  onSuccess?: (data: any) => boolean; // Define expected success handler signature
  note?: string; // Optional note for the test result UI
  treat404AsUnauthorized?: boolean; // Optional flag for role checking
}

// The main component that will be wrapped with FpjsProvider
function ApiTestPageContent() {
  const { supabase } = useSupabase();
  const { user, role: userRole } = useUser();
  const { authedFetch, isReady } = useAuthenticatedFetch();
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingTestData, setLoadingTestData] = useState(false);
  const [testData, setTestData] = useState<TestData>({
    TASK_ID: null,
    CASE_ID: null,
    CLIENT_ID: null,
    loaded: false,
    usingFallbacks: {
      TASK_ID: true,
      CASE_ID: true,
      CLIENT_ID: true
    }
  });

  // Helper function to get tenant_id from session (moved inside component)
  const getTenantIdFromSession = async (): Promise<string | null> => {
    const { data: { session } } = await supabase.auth.getSession(); // Now supabase is in scope
    if (session?.access_token) {
      const payload = parseJwtPayload(session.access_token);
      return payload?.tenant_id || null; // Adjust claim name if different
    }
    return null;
  };

  // Function to load real UUIDs from the database
  const loadTestData = async () => {
    if (!user) return;

    setLoadingTestData(true);
    const newTestData: TestData = {
      TASK_ID: null,
      CASE_ID: null,
      CLIENT_ID: null,
      loaded: false,
      usingFallbacks: {
        TASK_ID: true,
        CASE_ID: true,
        CLIENT_ID: true
      }
    };

    try {
      const tenantId = await getTenantIdFromSession();
      if (!tenantId) {
        console.error('Could not get tenantId from session for loading test data.');
        setLoadingTestData(false);
        return;
      }
      console.log('Loading test data for tenant:', tenantId);

      // Fetch most recent client
      const { data: clientData, error: clientError } = await supabase
        .from('clients')
        .select('id')
        .eq('tenant_id', tenantId) // Use extracted tenantId
        .limit(1)
        .order('created_at', { ascending: false });

      if (clientData && clientData.length > 0) {
        newTestData.CLIENT_ID = clientData[0].id;
        newTestData.usingFallbacks.CLIENT_ID = false;
        console.log('Found client ID:', newTestData.CLIENT_ID);
      } else {
        console.log('No clients found:', clientError);
      }

      // Fetch most recent case
      const { data: caseData, error: caseError } = await supabase
        .from('cases')
        .select('id')
        .eq('tenant_id', tenantId) // Use extracted tenantId
        .limit(1)
        .order('created_at', { ascending: false });

      if (caseData && caseData.length > 0) {
        newTestData.CASE_ID = caseData[0].id;
        newTestData.usingFallbacks.CASE_ID = false;
        console.log('Found case ID:', newTestData.CASE_ID);
      } else {
        console.log('No cases found:', caseError);
      }

      // Fetch most recent task
      const { data: taskData, error: taskError } = await supabase
        .from('tasks')
        .select('id')
        .eq('tenant_id', tenantId) // Use extracted tenantId
        .limit(1)
        .order('created_at', { ascending: false });

      if (taskData && taskData.length > 0) {
        newTestData.TASK_ID = taskData[0].id;
        newTestData.usingFallbacks.TASK_ID = false;
        console.log('Found task ID:', newTestData.TASK_ID);
      } else {
        console.log('No tasks found:', taskError);

        // If no tasks exist, create one for testing
        if (userRole && (userRole === 'partner' || userRole === 'attorney')) {
          try {
            console.log('Creating a test task...');
            const { data: newTask, error: createError } = await supabase
              .from('tasks')
              .insert([{
                title: 'API Test Task',
                description: 'Created for API testing',
                status: 'todo',
                tenant_id: tenantId, // Use extracted tenantId
                created_by: user?.id // Use user id from context
              }])
              .select('id');

            if (newTask && newTask.length > 0) {
              newTestData.TASK_ID = newTask[0].id;
              newTestData.usingFallbacks.TASK_ID = false;
              console.log('Created test task with ID:', newTestData.TASK_ID);
            } else {
              console.error('Failed to create test task:', createError);
            }
          } catch (error) {
            console.error('Error creating test task:', error);
          }
        }
      }

      // Mark as loaded and update state
      newTestData.loaded = true;
      setTestData(newTestData);

    } catch (error) {
      console.error('Error loading test data:', error);
    } finally {
      setLoadingTestData(false);
    }
  };

  // Load test data on component mount if user is available
  useEffect(() => {
    if (user && !testData.loaded) {
      loadTestData();
    }
  }, [user, testData.loaded]);

  const runTests = async () => {
    setLoading(true);
    setResults([]);

    const tenantId = await getTenantIdFromSession();
    if (!tenantId) {
      console.error('Could not get tenantId from session for running tests.');
      // Optionally set an error state or result here
      setLoading(false);
      return;
    }

    // Use fallback UUIDs if real ones aren't loaded
    const currentTaskId = testData.TASK_ID || '00000000-0000-0000-0000-000000000001';
    const currentCaseId = testData.CASE_ID || '00000000-0000-0000-0000-000000000002';
    const currentClientId = testData.CLIENT_ID || '00000000-0000-0000-0000-000000000003';

    // Define the tests to run using the interface
    const tests: TestDefinition[] = [
      {
        name: 'GET /api/test-auth',
        method: 'GET',
        url: '/api/test-auth',
        description: 'Should be accessible to all authenticated users',
        expectedRoles: ['partner', 'attorney', 'paralegal', 'staff', 'client']
      },
      {
        name: 'POST /api/test-auth',
        method: 'POST',
        url: '/api/test-auth',
        description: 'Should only be accessible to partners',
        expectedRoles: ['partner']
      },
      {
        name: 'PUT /api/test-auth',
        method: 'PUT',
        url: '/api/test-auth',
        description: 'Should be accessible to all staff roles',
        expectedRoles: ['partner', 'attorney', 'paralegal', 'staff']
      },
      {
        name: 'DELETE /api/test-auth',
        method: 'DELETE',
        url: '/api/test-auth',
        description: 'Should only be accessible to clients',
        expectedRoles: ['client']
      },
      // Tasks API tests
      {
        name: 'GET /api/tasks',
        method: 'GET',
        url: '/api/tasks',
        description: 'Should access tasks for staff roles',
        expectedRoles: ['partner', 'attorney', 'paralegal', 'staff'],
        onSuccess: (data: any) => {
          // Log JWT claims for debugging
          try {
            const session = supabase.auth.getSession();
            session.then(({ data }: { data: any }) => {
              if (data?.session?.access_token) {
                const jwtPayload = JSON.parse(atob(data.session.access_token.split('.')[1]));
                console.log('JWT claims in tasks test:', jwtPayload);
              }
            });
          } catch (e) {
            console.error('Error decoding JWT in test:', e);
          }

          console.log('Tasks test data:', data);
          return true;
        }
      },
      {
        name: 'GET /api/tasks/[id]',
        method: 'GET',
        url: `/api/tasks/${currentTaskId}`,
        description: 'Should fetch a specific task by ID',
        expectedRoles: ['partner', 'attorney', 'paralegal', 'staff']
      },
      {
        name: 'POST /api/tasks',
        method: 'POST',
        url: '/api/tasks',
        description: 'Should create a new task',
        expectedRoles: ['partner', 'attorney', 'paralegal', 'staff'],
        body: {
          title: 'Test Task from API Test',
          description: 'This task was created by the API test',
          status: 'todo',
          priority: 'medium',
          tenant_id: tenantId // Add tenant_id to ensure RLS policies are satisfied
        }
      },
      {
        name: 'PUT /api/tasks/[id]',
        method: 'PUT',
        url: `/api/tasks/${currentTaskId}`,
        description: 'Should update an existing task',
        expectedRoles: ['partner', 'attorney', 'paralegal', 'staff'],
        body: {
          title: 'Updated Test Task',
          description: 'This task was updated by the API test',
          status: 'in_progress'
        }
      },
      // Cases API tests
      {
        name: 'GET /api/cases',
        method: 'GET',
        url: '/api/cases',
        description: 'Should access cases for staff roles',
        expectedRoles: ['partner', 'attorney', 'paralegal', 'staff'],
        onSuccess: (data: any) => {
          console.log('Cases test data:', data);
          return true;
        }
      },
      {
        name: 'GET /api/cases/[id]',
        method: 'GET',
        url: `/api/cases/${currentCaseId}`,
        description: 'Should fetch a specific case by ID',
        expectedRoles: ['partner', 'attorney', 'paralegal', 'staff']
      },
      {
        name: 'POST /api/cases',
        method: 'POST',
        url: '/api/cases',
        description: 'Should create a new case',
        expectedRoles: ['partner', 'attorney'],
        body: {
          title: 'Test Case from API Test',
          description: 'This case was created by the API test',
          client_id: currentClientId,
          status: 'pending',
          tenant_id: tenantId // Add tenant_id to ensure RLS policies are satisfied
        }
      },
      {
        name: 'PUT /api/cases/[id]',
        method: 'PUT',
        url: `/api/cases/${currentCaseId}`,
        description: 'Should update an existing case',
        expectedRoles: ['partner', 'attorney', 'paralegal'],
        body: {
          title: 'Updated Test Case',
          description: 'This case was updated by the API test',
          status: 'active'
        }
      },
      // Clients API tests
      {
        name: 'GET /api/clients',
        method: 'GET',
        url: '/api/clients',
        description: 'Should access clients for staff roles',
        expectedRoles: ['partner', 'attorney', 'paralegal', 'staff']
      },
      {
        name: 'GET /api/clients/[id]',
        method: 'GET',
        url: `/api/clients/${currentClientId}`,
        description: 'Should fetch a specific client by ID',
        expectedRoles: ['partner', 'attorney', 'paralegal', 'staff']
      },
      {
        name: 'POST /api/clients',
        method: 'POST',
        url: '/api/clients',
        description: 'Should create a new client',
        expectedRoles: ['partner', 'attorney', 'paralegal'],
        body: {
          first_name: 'Test',
          last_name: 'Client',
          email: '<EMAIL>',
          phone_number: '************',
          client_type: 'individual', // Add required client_type field
          tenant_id: tenantId // Add tenant_id to ensure RLS policies are satisfied
        }
      },
      {
        name: 'PUT /api/clients/[id]',
        method: 'PUT',
        url: `/api/clients/${currentClientId}`,
        description: 'Should update an existing client',
        expectedRoles: ['partner', 'attorney', 'paralegal'],
        body: {
          first_name: 'Updated',
          last_name: 'Client',
          email: '<EMAIL>'
        }
      }
    ];

    // Run each test
    for (const test of tests) {
      try {
        // Skip tests that use fallback IDs for specific resources
        const usesNonExistentId =
          (test.url.includes(`/tasks/${currentTaskId}`) && testData.usingFallbacks.TASK_ID) ||
          (test.url.includes(`/cases/${currentCaseId}`) && testData.usingFallbacks.CASE_ID) ||
          (test.url.includes(`/clients/${currentClientId}`) && testData.usingFallbacks.CLIENT_ID);

        if (usesNonExistentId) {
          // Log a warning for tests that would fail due to missing resources
          console.warn(`Skipping test ${test.name} because it uses a fallback ID that likely doesn't exist`);
          setResults(prev => [...prev, {
            ...test,
            status: 'Skipped',
            isAuthorized: null,
            shouldBeAuthorized: test.expectedRoles.includes(userRole || ''),
            success: null,
            warning: 'Test skipped because it uses a fallback ID that likely does not exist'
          }]);
          continue; // Skip to next test
        }

        // Use authedFetch instead of fetch
        const options = {
          method: test.method,
          body: test.body ? JSON.stringify(test.body) : undefined,
          headers: test.body ? { 'Content-Type': 'application/json' } : undefined
        };

        // Add tenant_id to ensure RLS policies are satisfied
        const requestBody = test.method !== 'GET' ? {
          ...(test.body || {}),
          tenant_id: tenantId
        } : null;

        options.body = requestBody ? JSON.stringify(requestBody) : undefined;

        // Explicitly type the response variable
        const response: Response = await fetch(test.url, options);
        console.log(`Received response for ${test.name}: Status ${response.status}`);

        // Try to read response body safely
        let responseData: any = null;
        let responseText = '';
        try {
          if (response.headers.get('content-type')?.includes('application/json')) {
            responseData = await response.json();
          } else {
            responseText = await response.text();
            // Attempt to parse if it looks like JSON, otherwise keep as text
            try { responseData = JSON.parse(responseText); } catch { responseData = responseText; }
          }
        } catch (bodyError) {
          console.error(`Error reading response body for ${test.name}:`, bodyError);
          responseText = '[Error reading response body]';
        }

        const result = {
          name: test.name,
          description: test.description,
          url: test.url,
          method: test.method,
          status: response.status,
          statusText: response.statusText,
          expectedRoles: test.expectedRoles,
          data: responseData,
          error: null as string | null, // Initialize with correct type
          warning: testData.usingFallbacks.TASK_ID || testData.usingFallbacks.CASE_ID || testData.usingFallbacks.CLIENT_ID ? 'Test ran using fallback UUIDs.' : null,
          isAuthorized: false, // Default to false, check logic below
          note: test.note // Access optional property
        };

        const is404 = response.status === 404;
        const hasExpectedRole = test.expectedRoles.includes(userRole || '');

        // Determine authorization based on status and expected roles
        // If the user has the expected role, success is response.ok (2xx)
        // If the user does NOT have the expected role, success is 403 (Forbidden) or potentially 404 if endpoint hides unauthorized access
        if (hasExpectedRole) {
          result.isAuthorized = response.ok;
          if (!response.ok && result.error === null) {
             result.error = `Expected status 2xx for role '${userRole}', but got ${response.status}. Response: ${JSON.stringify(responseData || responseText)}`;
          }
        } else {
          // User does not have the expected role
          result.isAuthorized = response.status === 403 || (is404 && (test.treat404AsUnauthorized ?? false));
           if (!result.isAuthorized && result.error === null) {
             result.error = `Expected status 403 (or 404 if configured) for unauthorized role '${userRole}', but got ${response.status}. Response: ${JSON.stringify(responseData || responseText)}`;
          }
        }

        setResults(prev => [...prev, result]);
      } catch (error: any) {
        console.error(`Error running test ${test.name}:`, error);
        setResults(prev => [...prev, {
          ...test,
          status: 'Error',
          isAuthorized: false,
          shouldBeAuthorized: test.expectedRoles.includes(userRole || ''),
          success: false,
          error: error.message
        }]);
      }
    }

    setLoading(false);
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">API Authentication Test</h1>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Current User</CardTitle>
          <CardDescription>Authentication status and role information</CardDescription>
        </CardHeader>
        <CardContent>
          {user ? (
            <div>
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>Role:</strong> {userRole || 'Unknown'}</p>
              <p><strong>ID:</strong> {user.id}</p>
              <p><strong>Test Data Status:</strong> {testData.loaded ? 'Loaded' : 'Not Loaded'}</p>
              {testData.loaded && (
                <div className="mt-2">
                  <p><strong>Test Task ID:</strong> {testData.TASK_ID || 'None found'}</p>
                  <p><strong>Test Case ID:</strong> {testData.CASE_ID || 'None found'}</p>
                  <p><strong>Test Client ID:</strong> {testData.CLIENT_ID || 'None found'}</p>
                </div>
              )}
            </div>
          ) : (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Not Authenticated</AlertTitle>
              <AlertDescription>
                You are not currently logged in. Please sign in to test the API endpoints.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
        <CardFooter className="flex gap-2">
          <Button onClick={loadTestData} disabled={loadingTestData || !user}>
            {loadingTestData ? 'Loading Test Data...' : 'Refresh Test Data'}
          </Button>
          <Button onClick={runTests} disabled={loading || !user}>
            {loading ? 'Running Tests...' : 'Run API Tests'}
          </Button>
        </CardFooter>
      </Card>

      {testData.loaded && (
        <Alert
          variant={Object.values(testData.usingFallbacks).some(v => v) ? "destructive" : "default"}
          className="mb-6"
        >
          <AlertTitle className="flex items-center">
            {Object.values(testData.usingFallbacks).some(v => v)
              ? <AlertTriangle className="h-4 w-4 mr-2" />
              : <CheckCircle2 className="h-4 w-4 mr-2" />}
            Test Data Status
          </AlertTitle>
          <AlertDescription>
            <div className="mt-2">
              <p className="font-semibold">Current Test Data:</p>
              <ul className="list-disc pl-5 mt-1">
                <li className={testData.usingFallbacks.TASK_ID ? "text-red-500" : ""}>
                  Task ID: {testData.usingFallbacks.TASK_ID
                    ? "Using fallback (no real data found)"
                    : testData.TASK_ID}
                </li>
                <li className={testData.usingFallbacks.CASE_ID ? "text-red-500" : ""}>
                  Case ID: {testData.usingFallbacks.CASE_ID
                    ? "Using fallback (no real data found)"
                    : testData.CASE_ID}
                </li>
                <li className={testData.usingFallbacks.CLIENT_ID ? "text-red-500" : ""}>
                  Client ID: {testData.usingFallbacks.CLIENT_ID
                    ? "Using fallback (no real data found)"
                    : testData.CLIENT_ID}
                </li>
              </ul>
              {Object.values(testData.usingFallbacks).some(v => v) && (
                <p className="text-red-500 mt-2">
                  Warning: Some tests are using fallback UUIDs. This may cause errors if the UUIDs don&apos;t exist in your database.
                </p>
              )}
            </div>
          </AlertDescription>
        </Alert>
      )}

      <h2 className="text-xl font-semibold mb-4">Test Results</h2>

      {results.length > 0 ? (
        <div className="space-y-4">
          {results.map((result, index) => (
            <Card key={index} className={`mb-4 ${result.isAuthorized ? 'border-green-500' : 'border-red-500'}`}>
              <CardHeader>
                <CardTitle>{result.name}</CardTitle>
                <CardDescription>{result.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  {result.isAuthorized === null ? (
                    <AlertTriangle className="text-yellow-500" />
                  ) : result.isAuthorized ? (
                    <CheckCircle2 className="text-green-500" />
                  ) : (
                    <XCircle className="text-red-500" />
                  )}
                  <span>Status: {result.isAuthorized === null ? 'Skipped' : result.isAuthorized ? 'Authorized' : 'Unauthorized'}</span>
                </div>
                <div className="mt-2">
                  <strong>HTTP Status:</strong> {result.status} {result.statusText ? `(${result.statusText})` : ''}
                </div>
                {result.expectedRoles && (
                  <div className="mt-2">
                    <strong>Expected Roles:</strong> {result.expectedRoles.join(', ')}
                  </div>
                )}
                {result.warning && (
                  <div className="mt-2 text-yellow-600">
                    <strong>Warning:</strong> {result.warning}
                  </div>
                )}
                {result.note && (
                  <div className="mt-2 text-blue-600">
                    <strong>Note:</strong> {result.note}
                  </div>
                )}
                {result.error && (
                  <div className="mt-2 text-red-600">
                    <strong>Error:</strong> {result.error}
                  </div>
                )}
                {result.data && (
                  <div className="mt-2">
                    <details>
                      <summary className="cursor-pointer font-medium">Response Data</summary>
                      <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-40">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <p className="text-gray-500">Run the tests to see results here.</p>
      )}
    </div>
  );
}

// Export the wrapped component
export default function ApiTestPage() {
  return (
    <ApiTestPageContent />
  );
}

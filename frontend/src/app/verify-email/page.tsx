"use client"

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase/client'
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function VerifyEmailPage() {
  const router = useRouter()
  useEffect(() => {
    // Parse tokens from URL hash and set session
    const hash = window.location.hash
    if (hash) {
      const params = new URLSearchParams(hash.substring(1))
      const access_token = params.get('access_token')
      const refresh_token = params.get('refresh_token')
      if (access_token && refresh_token) {
        supabase.auth.setSession({ access_token, refresh_token })
          .then(({ error }) => {
            if (error) console.error('Error setting session:', error)
            else router.push('/onboarding/firm')
          })
      }
    }
  }, [router])
  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-md mx-auto text-center">
        <CardHeader>
          <CardTitle>Verify Your Email</CardTitle>
        </CardHeader>
        <CardContent>
          <p>
            A confirmation link has been sent to your email address. <br />
            Please check your inbox and click the link to verify your account. <br />
            If you don&apos;t see it in your inbox, please check your junk/spam folder.
          </p>
          <Link href="/login">
            <Button className="mt-4">Return to Login</Button>
          </Link>
        </CardContent>
      </Card>
    </div>
  )
}

'use client'

import React, { ReactNode } from 'react'
import { useRbac } from '@/hooks/useRbac'
import { useUser } from '@/contexts/UserContext'

interface RoleBasedComponentProps {
  /**
   * The roles that are allowed to see this component
   */
  allowedRoles?: string[]

  /**
   * The feature this component is part of
   * Will use the hasAccess method from useRbac
   */
  feature?: 'templates' | 'documents' | 'cases' | 'admin'

  /**
   * Whether to show a fallback component when the user doesn't have access
   */
  showFallback?: boolean

  /**
   * Custom fallback component to show when the user doesn't have access
   */
  fallback?: ReactNode

  /**
   * The content to render when the user has access
   */
  children: ReactNode
}

/**
 * Component that conditionally renders content based on user role
 * Uses JWT claims as the source of truth via UserContext
 */
export function RoleBasedComponent({
  allowedRoles,
  feature,
  showFallback = false,
  fallback = null,
  children
}: RoleBasedComponentProps) {
  const rbac = useRbac()
  const { loading } = useUser()

  // If still loading, don&apos;t render anything yet
  if (loading) {
    return null
  }

  // Check if user has access based on feature
  if (feature && rbac.hasAccess(feature)) {
    return <>{children}</>
  }

  // Check if user has access based on roles
  if (allowedRoles && rbac.hasRole(allowedRoles)) {
    return <>{children}</>
  }

  // If no access and fallback is enabled, show fallback
  if (showFallback) {
    return <>{fallback || <AccessDeniedFallback />}</>
  }

  // Otherwise render nothing
  return null
}

/**
 * Default fallback component when access is denied
 */
function AccessDeniedFallback() {
  return (
    <div className="p-4 border border-red-200 rounded bg-red-50 text-red-800">
      <h3 className="font-semibold">Access Denied</h3>
      <p>You don&apos;t have permission to view this content.</p>
    </div>
  )
}

/**
 * Higher-order component that wraps a component with role-based access control
 */
export function withRoleBasedAccess<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<RoleBasedComponentProps, 'children'>
) {
  return function WithRoleBasedAccess(props: P) {
    return (
      <RoleBasedComponent {...options}>
        <Component {...props} />
      </RoleBasedComponent>
    )
  }
}

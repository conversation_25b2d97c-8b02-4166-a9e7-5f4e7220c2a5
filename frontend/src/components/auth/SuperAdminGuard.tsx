'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useRbac } from '@/hooks/useRbac';
import { useUser } from '@/contexts/UserContext';

interface SuperAdminGuardProps {
  children: React.ReactNode;
  fallbackPath?: string;
  showLoading?: boolean;
}

/**
 * SuperAdminGuard - Higher Order Component for protecting super admin routes
 * 
 * This component checks if the current user has super admin privileges
 * based on the is_super_admin JWT claim. If not, it redirects to a fallback path.
 * 
 * @param children - The components to render if user has super admin access
 * @param fallbackPath - Path to redirect to if user lacks super admin access (default: '/')
 * @param showLoading - Whether to show loading state while checking permissions (default: true)
 */
export function SuperAdminGuard({ 
  children, 
  fallbackPath = '/', 
  showLoading = true 
}: SuperAdminGuardProps) {
  const { isSuperAdmin, isLoading } = useRbac();
  const { loading: userLoading } = useUser();
  const router = useRouter();

  const isCheckingAuth = isLoading() || userLoading;

  useEffect(() => {
    // Only redirect if we&apos;re done loading and user is not a super admin
    if (!isCheckingAuth && !isSuperAdmin()) {
      console.warn('SuperAdminGuard: Unauthorized access attempt to super admin route. Redirecting to:', fallbackPath);
      router.replace(fallbackPath);
    }
  }, [isCheckingAuth, isSuperAdmin, router, fallbackPath]);

  // Show loading state while checking authentication
  if (isCheckingAuth && showLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  // Don't render children if user is not a super admin
  if (!isSuperAdmin()) {
    return null;
  }

  // Render children if user has super admin access
  return <>{children}</>;
}

export default SuperAdminGuard;

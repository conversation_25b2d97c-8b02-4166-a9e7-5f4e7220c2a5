import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { toast } from '@/components/ui/use-toast';
import { AlertCircle, Calendar, FileText, Briefcase, CheckCircle, Clock } from 'lucide-react';
import { useSupabase } from '@/lib/supabase/provider';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

interface InsightActionsProps {
  insight: {
    id: string;
    feedbackId?: string;
    relatedEntity?: {
      type: string;
      id?: string;
      name?: string;
    };
    message: string;
  };
  onActionComplete?: (actionType: string, result: any) => void;
}

// Define the expected response structure for the integration API
interface IntegrationApiResponse {
  success: boolean;
  message?: string;
  data?: any; // Use a more specific type if known
}

/**
 * Component for executing bi-directional actions from insights
 * Connects insights to case management, document system, and deadlines
 */
export function InsightActions({ insight, onActionComplete }: InsightActionsProps) {
  const { supabase, session } = useSupabase();
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const { authedFetch } = useAuthenticatedFetch();

  // Determine available actions based on insight type and related entity
  const hasRelatedCase = insight.relatedEntity?.type === 'case' && insight.relatedEntity?.id;
  const hasRelatedDocument = insight.relatedEntity?.type === 'document' && insight.relatedEntity?.id;
  const isTimeRelated = insight.message.toLowerCase().includes('deadline') ||
                       insight.message.toLowerCase().includes('due date') ||
                       insight.message.toLowerCase().includes('scheduled');

  /**
   * Execute an integration action via the unified API
   */
  const executeAction = async (
    action: string,
    entityType: string,
    entityId: string | undefined,
    data: Record<string, unknown> = {}
  ) => {
    if (!entityId && entityType !== 'deadline') {
      toast({
        title: "Action Failed",
        description: `No ${entityType} ID available for this action`,
        variant: "destructive"
      });
      return;
    }

    setIsLoading(action);

    try {
      // Call authedFetch with the expected response type
      const result = await authedFetch<IntegrationApiResponse>('/api/integration', {
        method: 'POST',
        body: JSON.stringify({
          action,
          entityType,
          entityId,
          data: {
            ...data,
            insightId: insight.id
          }
        })
      });

      // Check the success flag in the response data
      if (result?.success) {
        toast({
          title: "Action Completed",
          description: result.message || `Successfully executed ${action.toLowerCase().replace(/_/g, ' ')}`,
          variant: "default"
        });

        if (onActionComplete) {
          onActionComplete(action, result.data);
        }
      } else {
         // Handle cases where the API might return success: false even with a 2xx status
         throw new Error(result?.message || `Action ${action} reported failure.`);
      }

    } catch (error) {
      // Errors thrown by authedFetch (HTTP errors, network errors) or the manual throw above are caught here
      console.error('Error executing integration action:', error);
      toast({
        title: "Action Failed",
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive"
      });
    } finally {
      setIsLoading(null);
    }
  };

  return (
    <div className="flex flex-wrap gap-2 mt-2">
      <TooltipProvider>
        {/* Case actions */}
        {hasRelatedCase && (
          <>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => executeAction(
                    'GENERATE_INSIGHTS',
                    'case',
                    insight.relatedEntity?.id
                  )}
                  disabled={isLoading !== null}
                >
                  <Briefcase className="h-4 w-4" />
                  <span className="hidden sm:inline">Analyze Case</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>Generate deeper insights for this case</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => executeAction(
                    'APPLY_ACTION',
                    'case',
                    insight.relatedEntity?.id,
                    {
                      actionType: 'UPDATE_PRIORITY',
                      actionData: {
                        priority: 'high',
                        reason: insight.message
                      }
                    }
                  )}
                  disabled={isLoading !== null}
                >
                  <AlertCircle className="h-4 w-4" />
                  <span className="hidden sm:inline">Flag Priority</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>Flag this case as high priority</TooltipContent>
            </Tooltip>
          </>
        )}

        {/* Document actions */}
        {hasRelatedDocument && (
          <>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => executeAction(
                    'GENERATE_DOCUMENT_INSIGHTS',
                    'document',
                    insight.relatedEntity?.id
                  )}
                  disabled={isLoading !== null}
                >
                  <FileText className="h-4 w-4" />
                  <span className="hidden sm:inline">Analyze Document</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>Generate insights for this document</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => executeAction(
                    'APPLY_INSIGHT_TO_DOCUMENT',
                    'document',
                    insight.relatedEntity?.id,
                    {
                      actionType: 'TAG_DOCUMENT',
                      actionData: {
                        tags: ['insight-flagged', 'needs-review']
                      }
                    }
                  )}
                  disabled={isLoading !== null}
                >
                  <CheckCircle className="h-4 w-4" />
                  <span className="hidden sm:inline">Tag for Review</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>Tag this document for review</TooltipContent>
            </Tooltip>
          </>
        )}

        {/* Time-related actions */}
        {isTimeRelated && (
          <>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => executeAction(
                    'GENERATE_TIME_INSIGHTS',
                    'deadline',
                    undefined,
                    { daysAhead: 14 }
                  )}
                  disabled={isLoading !== null}
                >
                  <Clock className="h-4 w-4" />
                  <span className="hidden sm:inline">Time Analysis</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>Analyze upcoming deadlines</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => {
                    // This would typically open a modal to create an event
                    // For simplicity, we&apos;re using a basic event structure
                    const tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    tomorrow.setHours(10, 0, 0, 0);

                    const endTime = new Date(tomorrow);
                    endTime.setHours(11, 0, 0, 0);

                    executeAction(
                      'ADD_CALENDAR_EVENT',
                      'deadline',
                      undefined,
                      {
                        insightId: insight.id,
                        eventData: {
                          title: `Follow up: ${insight.message.substring(0, 30)}...`,
                          description: insight.message,
                          start_time: tomorrow.toISOString(),
                          end_time: endTime.toISOString(),
                          case_id: insight.relatedEntity?.type === 'case' ? insight.relatedEntity.id : undefined
                        }
                      }
                    );
                  }}
                  disabled={isLoading !== null}
                >
                  <Calendar className="h-4 w-4" />
                  <span className="hidden sm:inline">Schedule</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>Add follow-up to calendar</TooltipContent>
            </Tooltip>
          </>
        )}

        {/* Find related documents - available for all insights */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={() => executeAction(
                'FIND_RELATED_DOCUMENTS',
                'document',
                undefined,
                {
                  insightText: insight.message,
                  caseId: insight.relatedEntity?.type === 'case' ? insight.relatedEntity.id : undefined
                }
              )}
              disabled={isLoading !== null}
            >
              <FileText className="h-4 w-4" />
              <span className="hidden sm:inline">Find Documents</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>Find related documents</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}

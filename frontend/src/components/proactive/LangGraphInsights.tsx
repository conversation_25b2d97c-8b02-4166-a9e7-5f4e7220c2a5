'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, CheckCircle, Star, MessageSquare, ThumbsUp, ThumbsDown } from 'lucide-react';
import { useSupabase } from '@/lib/supabase/provider';
import { useToast } from '@/components/ui/use-toast';
import { InsightFeedback } from '@/components/proactive/InsightFeedback';
import { cn } from '@/lib/utils';
import { Insight } from '@/lib/types';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

// Priority color mapping
const priorityColors: Record<number, string> = {
  1: 'text-gray-500 bg-gray-100',
  2: 'text-gray-600 bg-gray-100',
  3: 'text-blue-600 bg-blue-100',
  4: 'text-blue-800 bg-blue-100',
  5: 'text-amber-600 bg-amber-100',
  6: 'text-amber-700 bg-amber-100',
  7: 'text-orange-600 bg-orange-100',
  8: 'text-orange-700 bg-orange-100',
  9: 'text-red-700 bg-red-100',
  10: 'text-red-800 bg-red-100'
};

// Icon mapping for source types
const sourceIcons: Record<string, React.ReactNode> = {
  activity: <MessageSquare className="h-4 w-4" />,
  case: <AlertCircle className="h-4 w-4" />,
  document: <MessageSquare className="h-4 w-4" />,
  deadline: <CheckCircle className="h-4 w-4" />,
  proactive: <Star className="h-4 w-4" />,
  scheduled: <MessageSquare className="h-4 w-4" />
};

interface LangGraphInsightsProps {
  className?: string;
  maxInsights?: number;
  showTitle?: boolean;
  source?: string;
  entityId?: string;
  isLoading?: boolean;
  refreshInterval?: number; // in milliseconds, 0 to disable auto-refresh
  showFeedback?: boolean;
}

// --- API Response Interfaces ---
interface InsightsApiResponse {
  insights: Insight[];
}

interface FeedbackApiResponse {
  success: boolean;
  message?: string;
}
// --- End API Response Interfaces ---

export default function LangGraphInsights({
  className,
  maxInsights = 5,
  showTitle = true,
  source,
  entityId,
  isLoading: externalLoading,
  refreshInterval = 0,
  showFeedback = true
}: LangGraphInsightsProps) {
  const { user } = useSupabase();
  const { toast } = useToast();
  const { authedFetch } = useAuthenticatedFetch();

  const [insights, setInsights] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(!!externalLoading);
  const [error, setError] = useState<string | null>(null);
  const [selectedInsight, setSelectedInsight] = useState<any | null>(null);

  // Handle proactive insights fetching
  useEffect(() => {
    if (!user) return;

    fetchInsights();

    // Set up auto-refresh if enabled
    let refreshTimer: NodeJS.Timeout | null = null;
    if (refreshInterval > 0) {
      refreshTimer = setInterval(fetchInsights, refreshInterval);
    }

    return () => {
      if (refreshTimer) clearInterval(refreshTimer);
    };
  }, [user, source, entityId, refreshInterval]);

  // Function to fetch insights
  const fetchInsights = async () => {
    if (!user) return;

    try {
      setIsLoading(true);

      let url = '/api/insights/proactive';

      // Get timezone offset in minutes
      const timezoneOffset = new Date().getTimezoneOffset();

      // Add timezone offset to the request
      url += `?timezone=${timezoneOffset}&context=app_open`;

      // If we&apos;re fetching specific insights instead of proactive ones
      if (source) {
        url = '/api/insights/generate';

        // Call authedFetch with expected response type
        const responseData = await authedFetch<InsightsApiResponse>(url, {
          method: 'POST',
          body: JSON.stringify({
            source,
            entityId,
            limit: maxInsights * 2 // Fetch more than we need to allow for filtering
          })
        });

        // Use the insights array from the response data
        setInsights(responseData?.insights || []);
      } else {
        // Proactive insights - call authedFetch with expected response type
        const responseData = await authedFetch<InsightsApiResponse>(url);

        // Use the insights array from the response data
        setInsights(responseData?.insights || []);
      }
      setError(null); // Clear any previous error on success

    } catch (err: any) {
       console.error('Error fetching insights:', err);
       setError(err.message || 'Failed to fetch insights');
       setInsights([]); // Clear insights on error
    } finally {
      setIsLoading(false);
    }
  };

  // Function to provide feedback on an insight
  const handleFeedback = async (feedbackId: string, rating: number, comment: string = '') => {
    if (!feedbackId) {
      toast({ title: 'Error', description: 'Missing feedback ID', variant: 'destructive' });
      return;
    }

    try {
        // Call authedFetch with expected response type
        const responseData = await authedFetch<FeedbackApiResponse>('/api/insights/feedback', {
          method: 'POST',
          body: JSON.stringify({
            feedback_id: feedbackId,
            rating: rating,
            comment: comment || undefined
          })
        });

        // Check the success flag from the response data
        if (responseData?.success) {
            toast({ title: 'Feedback submitted', description: 'Thank you for your feedback!' });
            // Optionally update the local insight state to reflect feedback submission
            setInsights(prevInsights =>
              prevInsights.map(insight =>
                insight.feedbackId === feedbackId
                  ? { ...insight, feedback: rating } // Mark as feedback given
                  : insight
              )
            );
            setSelectedInsight(null); // Close modal if open
        } else {
             // Handle API returning success: false
             throw new Error(responseData?.message || 'Feedback submission reported failure.');
        }

    } catch (err: any) {
       console.error('Error submitting feedback:', err);
       toast({
         title: 'Feedback Error',
         description: err.message || 'Failed to submit feedback.',
         variant: 'destructive'
       });
    }
  };

  // Function to get a shortened list of high-priority insights
  const getDisplayInsights = () => {
    if (!insights.length) return [];

    // Sort by priority (high to low) and timestamp (newest first)
    return [...insights]
      .sort((a, b) => {
        // Primary sort by priority
        const priorityDiff = (b.priority || 5) - (a.priority || 5);
        if (priorityDiff !== 0) return priorityDiff;

        // Secondary sort by timestamp (newest first)
        return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
      })
      .slice(0, maxInsights);
  };

  // Display insights or loading/empty states
  const displayInsights = getDisplayInsights();

  return (
    <div className={cn('space-y-4', className)}>
      {showTitle && (
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">
            {source ? `${source.charAt(0).toUpperCase() + source.slice(1)} Insights` : 'Proactive Insights'}
          </h3>
          <Button variant="outline" size="sm" onClick={fetchInsights} disabled={isLoading}>
            Refresh
          </Button>
        </div>
      )}

      {isLoading ? (
        // Loading state
        <div className="space-y-3">
          {[...Array(3)].map((_, i) => (
            <Card key={`skeleton-${i}`} className="overflow-hidden">
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : error ? (
        // Error state
        <Card className="bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-700 flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Error Loading Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">{error}</p>
          </CardContent>
          <CardFooter>
            <Button variant="outline" onClick={fetchInsights}>Retry</Button>
          </CardFooter>
        </Card>
      ) : displayInsights.length === 0 ? (
        // Empty state
        <Card className="bg-gray-50">
          <CardHeader>
            <CardTitle className="text-gray-500">No Insights Available</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-400">
              {source
                ? `No insights found for this ${source}.`
                : "We don&apos;t have any insights for you right now. Check back later!"}
            </p>
          </CardContent>
        </Card>
      ) : (
        // Insights display
        <div className="space-y-3">
          {displayInsights.map((insight) => (
            <Card
              key={insight.id}
              className={cn(
                "border-l-4 transition-all hover:shadow-md",
                {
                  "border-l-blue-500": (insight.priority || 5) >= 7,
                  "border-l-amber-500": (insight.priority || 5) >= 5 && (insight.priority || 5) < 7,
                  "border-l-gray-300": (insight.priority || 5) < 5
                }
              )}
            >
              <CardHeader className="pb-2 flex flex-row items-start justify-between">
                <div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className={priorityColors[insight.priority || 5]}>
                      {sourceIcons[insight.source || 'activity']}
                      <span className="ml-1">{insight.source || 'Activity'}</span>
                    </Badge>
                    {insight.aiGenerated && (
                      <Badge variant="outline" className="bg-purple-50 text-purple-700">AI</Badge>
                    )}
                  </div>
                  <CardDescription className="mt-1 text-xs">
                    {new Date(insight.timestamp).toLocaleString()}
                  </CardDescription>
                </div>

                {/* Show feedback stars if feedback exists */}
                {insight.feedback && (
                  <div className="flex items-center text-amber-500">
                    {[...Array(insight.feedback)].map((_, i) => (
                      <Star key={i} className="h-3 w-3 fill-current" />
                    ))}
                  </div>
                )}
              </CardHeader>

              <CardContent>
                <p className="text-sm">{insight.message}</p>

                {insight.suggestions && (
                  <div className="mt-2">
                    <p className="text-xs font-medium text-gray-500">Suggestions:</p>
                    <p className="text-xs text-gray-600 mt-1">
                      {Array.isArray(insight.suggestions)
                        ? insight.suggestions.join(' • ')
                        : insight.suggestions}
                    </p>
                  </div>
                )}

                {insight.relatedEntity && (
                  <div className="mt-2">
                    <Badge variant="outline" className="text-xs">
                      Related to: {insight.relatedEntity.name || insight.relatedEntity.id}
                    </Badge>
                  </div>
                )}
              </CardContent>

              {showFeedback && (
                <CardFooter className="pt-0 flex justify-end">
                  {insight.feedback ? (
                    <div className="text-xs text-gray-500 italic">
                      Thank you for your feedback
                    </div>
                  ) : (
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-green-600 hover:text-green-700 hover:bg-green-50 p-1 h-auto"
                        onClick={() => handleFeedback(insight.feedbackId, 5)}
                      >
                        <ThumbsUp className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 p-1 h-auto"
                        onClick={() => handleFeedback(insight.feedbackId, 1)}
                      >
                        <ThumbsDown className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-600 text-xs hover:bg-gray-50"
                        onClick={() => setSelectedInsight(insight)}
                      >
                        Detailed Feedback
                      </Button>
                    </div>
                  )}
                </CardFooter>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* Feedback Modal */}
      {selectedInsight && (
        <InsightFeedback
          insight={selectedInsight as Insight}
          onFeedbackSubmit={(feedbackData: any) => {
            let rating = 5;
            if (feedbackData.action === 'not-helpful') {
              rating = 1;
            } else if (feedbackData.action === 'rated' && feedbackData.rating) {
              rating = feedbackData.rating;
            }
            handleFeedback(selectedInsight.feedbackId, rating, feedbackData.comment || '');
          }}
          onDismiss={() => setSelectedInsight(null)}
        />
      )}
    </div>
  );
}

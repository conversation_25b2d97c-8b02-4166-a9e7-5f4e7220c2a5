import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { InsightFeedback } from '../InsightFeedback';
import '@testing-library/jest-dom';

// Mock the Rating component
jest.mock('@/components/ui/rating', () => ({
  Rating: ({ value, onChange }: { value: number; onChange: (value: number) => void }) => (
    <div data-testid="mock-rating">
      <span>Rating: {value}</span>
      <button onClick={() => onChange(5)}>Rate 5</button>
      <button onClick={() => onChange(1)}>Rate 1</button>
    </div>
  )
}));

describe('InsightFeedback', () => {
  // Sample insight for testing
  const mockInsight = {
    id: 'insight-1',
    message: 'High Priority: Review settlement agreement',
    suggestions: ['View Case Details'],
    priority: 10,
    timestamp: '2025-04-10T10:00:00Z',
    feedbackId: 'feedback-123',
    relatedEntity: {
      type: 'case',
      name: 'Smith v. Acme Corp'
    }
  };

  // Mock feedback handler
  const mockOnFeedbackSubmit = jest.fn();
  const mockOnDismiss = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the initial feedback options', () => {
    render(
      <InsightFeedback
        insight={mockInsight}
        onFeedbackSubmit={mockOnFeedbackSubmit}
        onDismiss={mockOnDismiss}
      />
    );

    // Check if the initial question is displayed
    expect(screen.getByText('Was this insight helpful?')).toBeInTheDocument();

    // Check if the buttons are displayed
    expect(screen.getByText('Yes')).toBeInTheDocument();
    expect(screen.getByText('No')).toBeInTheDocument();
    expect(screen.getByText('Details')).toBeInTheDocument();
    expect(screen.getByText('Dismiss')).toBeInTheDocument();
  });

  it('should submit quick feedback when Yes is clicked', () => {
    render(
      <InsightFeedback
        insight={mockInsight}
        onFeedbackSubmit={mockOnFeedbackSubmit}
      />
    );

    // Click the Yes button
    fireEvent.click(screen.getByText('Yes'));

    // Check if the feedback was submitted
    expect(mockOnFeedbackSubmit).toHaveBeenCalledWith({
      feedbackId: mockInsight.feedbackId,
      insightId: mockInsight.id,
      action: 'helpful'
    });

    // Check if the thank you message is displayed
    expect(screen.getByText('Thanks for your feedback!')).toBeInTheDocument();
  });

  it('should submit quick feedback when No is clicked', () => {
    render(
      <InsightFeedback
        insight={mockInsight}
        onFeedbackSubmit={mockOnFeedbackSubmit}
      />
    );

    // Click the No button
    fireEvent.click(screen.getByText('No'));

    // Check if the feedback was submitted
    expect(mockOnFeedbackSubmit).toHaveBeenCalledWith({
      feedbackId: mockInsight.feedbackId,
      insightId: mockInsight.id,
      action: 'not-helpful'
    });

    // Check if the thank you message is displayed
    expect(screen.getByText('Thanks for your feedback!')).toBeInTheDocument();
  });

  it('should show detailed feedback form when Details is clicked', () => {
    render(
      <InsightFeedback
        insight={mockInsight}
        onFeedbackSubmit={mockOnFeedbackSubmit}
      />
    );

    // Click the Details button
    fireEvent.click(screen.getByText('Details'));

    // Check if the detailed form is displayed
    expect(screen.getByText('Rate this insight:')).toBeInTheDocument();
    expect(screen.getByText('Comments (optional):')).toBeInTheDocument();
    expect(screen.getByText('Submit Feedback')).toBeInTheDocument();
  });

  it('should submit detailed feedback with rating and comment', () => {
    render(
      <InsightFeedback
        insight={mockInsight}
        onFeedbackSubmit={mockOnFeedbackSubmit}
      />
    );

    // Click the Details button
    fireEvent.click(screen.getByText('Details'));

    // Set rating
    fireEvent.click(screen.getByText('Rate 5'));

    // Add comment
    const textarea = screen.getByPlaceholderText('Tell us more about your experience with this insight...');
    fireEvent.change(textarea, { target: { value: 'This was very helpful!' } });

    // Submit feedback
    fireEvent.click(screen.getByText('Submit Feedback'));

    // Check if the feedback was submitted
    expect(mockOnFeedbackSubmit).toHaveBeenCalledWith({
      feedbackId: mockInsight.feedbackId,
      insightId: mockInsight.id,
      action: 'rated',
      rating: 5,
      comment: 'This was very helpful!'
    });

    // Check if the thank you message is displayed
    expect(screen.getByText('Thanks for your feedback!')).toBeInTheDocument();
  });

  it('should call onDismiss when dismiss button is clicked', () => {
    render(
      <InsightFeedback
        insight={mockInsight}
        onFeedbackSubmit={mockOnFeedbackSubmit}
        onDismiss={mockOnDismiss}
      />
    );

    // Click the Dismiss button
    fireEvent.click(screen.getByText('Dismiss'));

    // Check if onDismiss was called
    expect(mockOnDismiss).toHaveBeenCalled();
  });

  it('should not render if insight has no feedbackId', () => {
    const insightWithoutFeedbackId = { ...mockInsight, feedbackId: undefined };

    const { container } = render(
      <InsightFeedback
        insight={insightWithoutFeedbackId}
        onFeedbackSubmit={mockOnFeedbackSubmit}
      />
    );

    // Check if nothing was rendered
    expect(container.firstChild).toBeNull();
  });

  it('should disable Submit button until rating is provided', () => {
    render(
      <InsightFeedback
        insight={mockInsight}
        onFeedbackSubmit={mockOnFeedbackSubmit}
      />
    );

    // Click the Details button
    fireEvent.click(screen.getByText('Details'));

    // Check if Submit button is disabled
    expect(screen.getByText('Submit Feedback')).toBeDisabled();

    // Set rating
    fireEvent.click(screen.getByText('Rate 5'));

    // Check if Submit button is enabled
    expect(screen.getByText('Submit Feedback')).not.toBeDisabled();
  });

  it('should cancel detailed feedback form', () => {
    render(
      <InsightFeedback
        insight={mockInsight}
        onFeedbackSubmit={mockOnFeedbackSubmit}
      />
    );

    // Click the Details button
    fireEvent.click(screen.getByText('Details'));

    // Click the Cancel button
    fireEvent.click(screen.getByText('Cancel'));

    // Check if we&apos;re back to the initial state
    expect(screen.getByText('Was this insight helpful?')).toBeInTheDocument();
  });
});

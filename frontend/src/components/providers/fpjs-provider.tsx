'use client'; // Mark as a client component

import React from 'react';
import { FpjsProvider } from '@fingerprintjs/fingerprintjs-pro-react';

// Use the API key from environment variable or fallback to hardcoded value
const FPJS_PUBLIC_API_KEY = process.env.NEXT_PUBLIC_FPJS_API_KEY || "2a3tGfti5KfngcidlMt5";

export function FingerprintJSClientProvider({ children }: { children: React.ReactNode }) {
  if (!FPJS_PUBLIC_API_KEY) {
    console.warn('FingerprintJS API Key is missing. FPJS Provider not initialized.');
    // Render children directly if key is missing, effectively disabling fingerprinting
    return <>{children}</>;
  }

  return (
    <FpjsProvider
      loadOptions={{
        apiKey: FPJS_PUBLIC_API_KEY
        // Removing endpoint as it may be causing issues
      }}
      // Removing cacheLocation as it's causing type errors
      // Removing cacheTimeInSeconds as it depends on cacheLocation
      // Ensure we&apos;re properly handling errors
      onError={(err: Error) => {
        console.error('FingerprintJS error:', err);
      }}
    >
      {children}
    </FpjsProvider>
  );
}

/**
 * Legal Research Chat Wrapper
 *
 * This component wraps CopilotChat to handle TypeScript compatibility issues.
 * It's specifically designed to work with the existing CopilotKit setup.
 */
import React from 'react';
import { CopilotChat } from "@copilotkit/react-ui";

// Extending the props with any to handle additional props used in practice
interface ExtendedCopilotChatProps extends React.ComponentProps<typeof CopilotChat> {
  // Add any additional props that we&apos;re using but TypeScript doesn't recognize
  context?: Record<string, unknown>;
  displayName?: string;
  placeholder?: string;
  suggestions?: string[];
  initialMessage?: string;
  agent?: string;
  [key: string]: any;
}

/**
 * Enhanced CopilotChat component that supports additional props
 * that are used in the application but not recognized by TypeScript
 */
export function LegalResearchChat(props: ExtendedCopilotChatProps) {
  // We use type assertion to bypass TypeScript's strict checking
  return <CopilotChat {...props as any} />;
}

/**
 * Case-integrated research component
 */
interface CaseContextResearchProps {
  caseId: string;
  caseTitle: string;
  caseType?: string;
  jurisdiction?: string;
  documents?: Array<{id: string; title: string; type: string}>;
}

export function CaseResearchComponent({
  caseId,
  caseTitle,
  caseType = "Personal Injury",
  jurisdiction = "Texas",
  documents = []
}: CaseContextResearchProps) {
  return (
    <div className="border rounded-md overflow-hidden shadow-sm">
      <div className="p-3 border-b bg-muted/20">
        <h3 className="font-medium">Case-Specific Research: {caseTitle}</h3>
      </div>
      <div className="min-h-[350px]">
        <LegalResearchChat
          className="h-full w-full"
          context={{
            agentType: "research_agent",
            caseId,
            caseTitle,
            caseType,
            jurisdiction,
            documentCount: documents?.length || 0
          }}
          initialMessage={`I'm ready to help with legal research for your case "${caseTitle}". This is a ${caseType} case in ${jurisdiction} jurisdiction with ${documents.length} associated documents. How can I assist you?`}
        />
      </div>
    </div>
  );
}

/**
 * Standalone research sidebar component
 */
export function ResearchSidebarComponent() {
  const [isVisible, setIsVisible] = React.useState(false);

  return (
    <div className="relative">
      {/* Toggle button for mobile/responsive design */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 md:hidden bg-primary text-white rounded-full p-4 shadow-lg z-50"
      >
        {isVisible ? "Close Research" : "Legal Research"}
      </button>

      {/* Sidebar container with responsive visibility */}
      <div className={`${
        isVisible ? 'translate-x-0' : 'translate-x-full md:translate-x-0'
      } fixed right-0 top-0 bottom-0 z-40 transition-transform duration-300 ease-in-out`}>
        <LegalResearchChat
          className="h-full w-[350px] md:w-[400px] border-l shadow-lg bg-background"
          context={{
            agentType: "research_agent",
            jurisdiction: "Texas",
            practiceArea: "Personal Injury",
            userRole: "attorney"
          }}
          displayName="Legal Research Assistant"
          placeholder="Ask me a legal research question..."
          initialMessage="I can help with Texas legal research questions related to personal injury law. I'll search relevant statutes, case law, and legal principles to provide accurate answers with proper citations."
        />
      </div>
    </div>
  );
}

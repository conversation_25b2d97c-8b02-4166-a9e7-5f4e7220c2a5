import { CopilotChat } from "@copilotkit/react-ui";
import { useState } from "react";
import { ErrorBoundary } from 'react-error-boundary';

export default function ResearchSidebar() {
  const [isVisible, setIsVisible] = useState(false);

  const legalResearchPrompts = [
    "What's the statute of limitations for personal injury in Texas?",
    "Summarize recent Texas case law on premises liability",
    "What are the damages caps for medical malpractice in Texas?",
    "Explain the comparative negligence laws in Texas",
    "What evidence is needed to prove negligence in a car accident case?"
  ];

  return (
    <div className="relative">
      {/* Toggle button for mobile/responsive design */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 md:hidden bg-primary text-white rounded-full p-4 shadow-lg z-50"
      >
        {isVisible ? "Close Research" : "Legal Research"}
      </button>

      {/* Sidebar container with responsive visibility */}
      <div className={`${
        isVisible ? 'translate-x-0' : 'translate-x-full md:translate-x-0'
      } fixed right-0 top-0 bottom-0 z-40 transition-transform duration-300 ease-in-out`}>
        <ErrorBoundary
          fallback={<div className="p-4 text-red-500">Failed to load research assistant</div>}
          onError={(error: Error) => console.error('Research sidebar error:', error)}
        >
          {/* Matching the props used in your existing CopilotChatComponent */}

          <CopilotChat
            className="h-full w-[350px] md:w-[400px] border-l shadow-lg bg-background"
            displayName="Legal Research Assistant"
            placeholder="Ask me a legal research question..."
            suggestions={legalResearchPrompts}
            context={{
              agentType: "research_agent",
              jurisdiction: "Texas",
              practiceArea: "Personal Injury",
              userRole: "attorney"
            }}
            initialMessage="I can help with Texas legal research questions related to personal injury law. I'll search relevant statutes, case law, and legal principles to provide accurate answers with proper citations."
          />
        </ErrorBoundary>
      </div>
    </div>
  );
}

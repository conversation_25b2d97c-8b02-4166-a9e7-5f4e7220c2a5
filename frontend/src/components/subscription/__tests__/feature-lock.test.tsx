import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { FeatureLock, useFeatureLock, withFeatureLock } from '../feature-lock';

// Mock the useFeatures hook
jest.mock('@/hooks/useFeatures', () => ({
  useFeatures: jest.fn(),
}));

const mockUseFeatures = require('@/hooks/useFeatures').useFeatures;

describe('FeatureLock', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders children when feature is available', () => {
    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(true),
      isLoading: false,
    });

    render(
      <FeatureLock featureKey="voice_intake">
        <div>Protected Content</div>
      </FeatureLock>
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
    expect(screen.queryByText(/locked/i)).not.toBeInTheDocument();
  });

  it('shows lock UI when feature is not available', () => {
    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(false),
      isLoading: false,
    });

    render(
      <FeatureLock featureKey="voice_intake" title="Voice Intake">
        <div>Protected Content</div>
      </FeatureLock>
    );

    expect(screen.getByText('Voice Intake')).toBeInTheDocument();
    expect(screen.getByText('Upgrade Now')).toBeInTheDocument();
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
  });

  it('shows loading state', () => {
    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(false),
      isLoading: true,
    });

    render(
      <FeatureLock featureKey="voice_intake">
        <div>Protected Content</div>
      </FeatureLock>
    );

    // Should show loading placeholder
    expect(screen.getByRole('generic')).toHaveClass('animate-pulse');
  });

  it('uses custom fallback when provided', () => {
    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(false),
      isLoading: false,
    });

    render(
      <FeatureLock 
        featureKey="voice_intake" 
        fallback={<div>Custom Fallback</div>}
      >
        <div>Protected Content</div>
      </FeatureLock>
    );

    expect(screen.getByText('Custom Fallback')).toBeInTheDocument();
    expect(screen.queryByText('Upgrade Now')).not.toBeInTheDocument();
  });

  it('opens upgrade modal when upgrade button is clicked', () => {
    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(false),
      isLoading: false,
    });

    render(
      <FeatureLock featureKey="voice_intake">
        <div>Protected Content</div>
      </FeatureLock>
    );

    const upgradeButton = screen.getByText('Upgrade Now');
    fireEvent.click(upgradeButton);

    // Modal should be opened (we can&apos;t easily test the modal content without more setup)
    expect(upgradeButton).toBeInTheDocument();
  });

  it('shows overlay mode correctly', () => {
    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(false),
      isLoading: false,
    });

    render(
      <FeatureLock featureKey="voice_intake" overlay={true}>
        <div>Protected Content</div>
      </FeatureLock>
    );

    // In overlay mode, content should be present but blurred
    expect(screen.getByText('Protected Content')).toBeInTheDocument();
    expect(screen.getByText('Protected Content').closest('.filter')).toHaveClass('blur-sm');
    expect(screen.getByText('Upgrade Now')).toBeInTheDocument();
  });

  it('formats feature names correctly', () => {
    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(false),
      isLoading: false,
    });

    render(
      <FeatureLock featureKey="voice_intake">
        <div>Protected Content</div>
      </FeatureLock>
    );

    expect(screen.getByText('Voice Intake Locked')).toBeInTheDocument();
  });
});

describe('useFeatureLock', () => {
  it('returns true when feature is locked', () => {
    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(false),
      isLoading: false,
    });

    const TestComponent = () => {
      const isLocked = useFeatureLock('voice_intake');
      return <div>{isLocked ? 'Locked' : 'Unlocked'}</div>;
    };

    render(<TestComponent />);
    expect(screen.getByText('Locked')).toBeInTheDocument();
  });

  it('returns false when feature is available', () => {
    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(true),
      isLoading: false,
    });

    const TestComponent = () => {
      const isLocked = useFeatureLock('voice_intake');
      return <div>{isLocked ? 'Locked' : 'Unlocked'}</div>;
    };

    render(<TestComponent />);
    expect(screen.getByText('Unlocked')).toBeInTheDocument();
  });

  it('returns false while loading', () => {
    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(false),
      isLoading: true,
    });

    const TestComponent = () => {
      const isLocked = useFeatureLock('voice_intake');
      return <div>{isLocked ? 'Locked' : 'Unlocked'}</div>;
    };

    render(<TestComponent />);
    expect(screen.getByText('Unlocked')).toBeInTheDocument();
  });
});

describe('withFeatureLock', () => {
  it('wraps component with feature lock', () => {
    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(false),
      isLoading: false,
    });

    const TestComponent = () => <div>Test Component</div>;
    const WrappedComponent = withFeatureLock('voice_intake')(TestComponent);

    render(<WrappedComponent />);

    expect(screen.queryByText('Test Component')).not.toBeInTheDocument();
    expect(screen.getByText('Upgrade Now')).toBeInTheDocument();
  });

  it('shows component when feature is available', () => {
    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(true),
      isLoading: false,
    });

    const TestComponent = () => <div>Test Component</div>;
    const WrappedComponent = withFeatureLock('voice_intake')(TestComponent);

    render(<WrappedComponent />);

    expect(screen.getByText('Test Component')).toBeInTheDocument();
    expect(screen.queryByText('Upgrade Now')).not.toBeInTheDocument();
  });
});

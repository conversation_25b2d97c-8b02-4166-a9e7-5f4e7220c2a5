'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSupabase } from '@/lib/supabase/provider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { Button } from '@/components/ui/button';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { format, subMonths } from 'date-fns';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

// --- API Response Interfaces ---
interface UsageEntry {
  periodStart: string; // ISO Date string
  periodEnd: string; // ISO Date string
  usageCount: number;
  size?: number; // Optional, for storage usage
}

interface QuotaInfo {
  usageType: string;
  totalUsage: number;
  limit: number | null; // Limit might be null if unlimited
  percentUsed: number;
  periodStart: string; // ISO Date string
  periodEnd: string; // ISO Date string
}

interface UsageApiResponse {
  usage: UsageEntry[];
}

interface QuotaApiResponse {
  quota: QuotaInfo;
}
// --- End API Response Interfaces ---

interface UsageDashboardProps {
  tenantId: string;
}

export function UsageDashboard({ tenantId }: UsageDashboardProps) {
  const { supabase } = useSupabase();
  const { authedFetch, isReady } = useAuthenticatedFetch();
  const [loading, setLoading] = useState(true);
  const [usageData, setUsageData] = useState<UsageEntry[]>([]);
  const [quotaData, setQuotaData] = useState<QuotaInfo | null>(null);
  const [startDate, setStartDate] = useState<Date>(subMonths(new Date(), 1));
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [selectedUsageType, setSelectedUsageType] = useState<string>('document_upload');
  const [error, setError] = useState<string | null>(null);

  const loadUsageData = useCallback(async () => {
    setLoading(true);
    setError(null);
    if (!authedFetch || !isReady) {
      console.warn('UsageDashboard: authedFetch not ready.');
      setError('Service not available to load usage data.');
      setLoading(false);
      return;
    }

    try {
      // Fetch usage data
      const usageResponseData = await authedFetch<UsageApiResponse>(
        `/api/subscription/usage?type=${selectedUsageType}&startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`
      );
      setUsageData(usageResponseData?.usage || []);

      // Fetch quota data - Reuse readiness check
      if (!authedFetch || !isReady) {
        console.warn('UsageDashboard: authedFetch became unready before fetching quota.');
        setLoading(false);
        return;
      }
      const quotaResponseData = await authedFetch<QuotaApiResponse>('/api/subscription/usage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          usageType: selectedUsageType,
          incrementBy: 0, // Just checking, not incrementing
        }),
      });

      setQuotaData(quotaResponseData?.quota || null);
    } catch (err) {
      console.error('Error loading usage data:', err);
      setError('Failed to load usage data');
    } finally {
      setLoading(false);
    }
  }, [startDate, endDate, selectedUsageType, authedFetch, isReady, setError]);

  useEffect(() => {
    loadUsageData();
  }, [loadUsageData]);

  // Wrapper functions for DatePicker setDate prop
  const handleStartDateSelect = (date: Date | undefined) => {
    if (date) {
      setStartDate(date);
    } else {
      // Handle undefined case if needed, e.g., set to a default or log
      // For now, we just won&apos;t update if undefined
    }
  };

  const handleEndDateSelect = (date: Date | undefined) => {
    if (date) {
      setEndDate(date);
    } else {
      // Handle undefined case if needed
    }
  };

  // Prepare chart data
  const prepareChartData = () => {
    if (!usageData || !Array.isArray(usageData)) return [];

    return usageData.map((usage: UsageEntry) => ({
      date: format(new Date(usage.periodStart), 'MMM dd'),
      usage: usage.usageCount,
      size: usage.size ? Math.round(usage.size / 1024 / 1024) : 0, // Convert to MB
    }));
  };

  const chartData = prepareChartData();

  // Calculate total usage
  const calculateTotalUsage = () => {
    if (!usageData || !Array.isArray(usageData)) return 0;

    return usageData.reduce((total: number, usage: UsageEntry) => total + usage.usageCount, 0);
  };

  const totalUsage = calculateTotalUsage();

  // Format usage value based on type
  const formatUsageValue = (value: number) => {
    if (selectedUsageType.startsWith('voice_agent')) {
      // Convert seconds to minutes and round to 1 decimal place
      return (value / 60).toFixed(1);
    }
    return value;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Resource Usage</CardTitle>
        <CardDescription>Track your resource usage over time</CardDescription>
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="flex-1">
              <label className="text-sm font-medium mb-1 block">Resource Type</label>
              <Select
                value={selectedUsageType}
                onValueChange={setSelectedUsageType}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select resource type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="document_upload">Document Uploads</SelectItem>
                  <SelectItem value="document_processing">Document Processing</SelectItem>
                  <SelectItem value="api_calls">API Calls</SelectItem>
                  <SelectItem value="storage_usage">Storage Usage</SelectItem>
                  <SelectItem value="ai_tokens_total">AI Tokens (Total)</SelectItem>
                  <SelectItem value="ai_tokens_input">AI Tokens (Input)</SelectItem>
                  <SelectItem value="ai_tokens_output">AI Tokens (Output)</SelectItem>
                  <SelectItem value="voice_agent_inbound">Voice Agent (Inbound)</SelectItem>
                  <SelectItem value="voice_agent_outbound">Voice Agent (Outbound)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex-1">
              <label className="text-sm font-medium mb-1 block">Start Date</label>
              <DatePicker
                date={startDate}
                setDate={handleStartDateSelect}
                className="w-full"
              />
            </div>

            <div className="flex-1">
              <label className="text-sm font-medium mb-1 block">End Date</label>
              <DatePicker
                date={endDate}
                setDate={handleEndDateSelect}
                className="w-full"
              />
            </div>

            <div className="flex items-end">
              <Button onClick={loadUsageData} disabled={loading}>
                {loading ? 'Loading...' : 'Refresh'}
              </Button>
            </div>
          </div>

          {error && (
            <div className="text-red-500 mb-4">{error}</div>
          )}

          {quotaData && quotaData.percentUsed >= 100 && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4 flex items-center">
              <svg className="h-5 w-5 mr-2 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
              </svg>
              <div>
                <p className="font-medium">Quota Exceeded</p>
                <p className="text-sm">You have exceeded your {selectedUsageType.replace('_', ' ')} quota. Please upgrade your plan to continue using this feature.</p>
              </div>
            </div>
          )}

          {quotaData && quotaData.percentUsed >= 80 && quotaData.percentUsed < 100 && (
            <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md mb-4 flex items-center">
              <svg className="h-5 w-5 mr-2 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
              </svg>
              <div>
                <p className="font-medium">Approaching Quota Limit</p>
                <p className="text-sm">You are approaching your {selectedUsageType.replace('_', ' ')} quota limit. Consider upgrading your plan soon.</p>
              </div>
            </div>
          )}

          <Tabs defaultValue="overview">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="chart">Chart</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4 mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Current Usage</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">{formatUsageValue(totalUsage)}</div>
                    <div className="text-sm text-gray-500 mt-1">
                      {selectedUsageType === 'storage_usage' ? 'MB used' :
                       selectedUsageType.startsWith('ai_tokens') ? 'Tokens used' :
                       selectedUsageType.startsWith('voice_agent') ? 'Minutes used' :
                       'Total count'}
                    </div>

                    {quotaData && (
                      <div className="mt-4 space-y-1">
                        <div className="flex justify-between text-sm">
                          <span>Usage</span>
                          <span>
                            {selectedUsageType.startsWith('voice_agent')
                              ? `${quotaData ? (quotaData.totalUsage / 60).toFixed(1) : 0} / ${quotaData && (quotaData.limit === -1 || quotaData.limit === null) ? 'Unlimited' : quotaData?.limit ? (quotaData.limit / 60).toFixed(1) : 0} min`
                              : `${quotaData?.totalUsage ?? 0} / ${quotaData && (quotaData.limit === -1 || quotaData.limit === null) ? 'Unlimited' : quotaData?.limit ?? 'N/A'}`}
                          </span>
                        </div>
                        {quotaData && quotaData.limit === -1 || quotaData?.limit === null ? (
                          <>
                            <Progress
                              value={0}
                              className="h-2 bg-gray-200"
                            />
                            <div className="text-xs text-gray-500">
                              <span>Unlimited usage</span>
                            </div>
                          </>
                        ) : (
                          <>
                            <Progress
                              value={quotaData.percentUsed}
                              className={`h-2 ${
                                quotaData.percentUsed >= 100 ? 'bg-red-200' :
                                quotaData.percentUsed >= 80 ? 'bg-yellow-200' :
                                'bg-gray-200'
                              }`}
                              indicatorClassName={`${
                                quotaData.percentUsed >= 100 ? 'bg-red-500' :
                                quotaData.percentUsed >= 80 ? 'bg-yellow-500' :
                                ''
                              }`}
                            />
                            <div className={`text-xs ${
                              quotaData.percentUsed >= 100 ? 'text-red-600 font-medium' :
                              quotaData.percentUsed >= 80 ? 'text-yellow-600 font-medium' :
                              'text-gray-500'
                            }`}>
                              {quotaData.percentUsed >= 100 ? (
                                <span>Quota exceeded! {quotaData.percentUsed}% used</span>
                              ) : quotaData.percentUsed >= 80 ? (
                                <span>Approaching limit! {quotaData.percentUsed}% used</span>
                              ) : (
                                <span>{quotaData.percentUsed}% of quota used</span>
                              )}
                            </div>
                          </>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Usage Breakdown</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {!loading && usageData && Array.isArray(usageData) && usageData.length > 0 ? (
                      <div className="space-y-2">
                        {usageData.map((usage: UsageEntry, index: number) => (
                          <div key={index} className="flex justify-between items-center">
                            <span className="text-sm">
                              {format(new Date(usage.periodStart), 'MMM dd')} - {format(new Date(usage.periodEnd), 'MMM dd')}
                            </span>
                            <span className="font-medium">
                              {selectedUsageType.startsWith('voice_agent')
                                ? `${(usage.usageCount / 60).toFixed(1)} min`
                                : usage.usageCount}
                            </span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-gray-500">No usage data available</div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="chart" className="mt-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Usage Over Time</CardTitle>
                </CardHeader>
                <CardContent>
                  {!loading && chartData.length > 0 ? (
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={chartData}
                          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis />
                          <Tooltip />
                          <Bar dataKey="usage" fill="#4f46e5" name="Usage Count" />
                          {selectedUsageType === 'storage_usage' && (
                            <Bar dataKey="size" fill="#10b981" name="Size (MB)" />
                          )}
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  ) : (
                    <div className="h-80 flex items-center justify-center text-gray-500">
                      {loading ? 'Loading chart data...' : 'No chart data available'}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  );
}

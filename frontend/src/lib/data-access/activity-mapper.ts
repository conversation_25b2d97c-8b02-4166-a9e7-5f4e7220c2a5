/**
 * Activity Data Mapper
 *
 * Maps between Neo4j records and domain models for activities.
 * Unlike other data mappers which primarily interact with Supabase,
 * this mapper works with Neo4j to handle the graph-based activity data.
 */

import { z } from 'zod';
import neo4j, { Driver, Record as Neo4jRecord, Result } from 'neo4j-driver';
import { getDriver } from '@/lib/neo4j/client';

// Enums
export enum ActivityType {
  CASE_CREATED = 'case_created',
  CASE_UPDATED = 'case_updated',
  CLIENT_ADDED = 'client_added',
  DOCUMENT_UPLOADED = 'document_uploaded',
  TASK_CREATED = 'task_created',
  TASK_UPDATED = 'task_updated',
  TASK_COMPLETED = 'task_completed',
  DEADLINE_SET = 'deadline_set',
  DEADLINE_MET = 'deadline_met',
  DEADLINE_MISSED = 'deadline_missed',
  DOCUMENT_GENERATED = 'document_generated',
  DOCUMENT_SIGNED = 'document_signed',
  INSIGHT_GENERATED = 'insight_generated',
  LOGIN = 'login',
  COMMENT_ADDED = 'comment_added'
}

// Domain Model Interfaces
export interface Activity {
  id: string;
  activityType: string;
  title: string;
  description: string | null;
  userId: string | null;
  caseId: string | null;
  tenantId: string;
  data: Record<string, unknown> | null;
  createdAt: Date | null;
  updatedAt: Date | null;

  // Relations (populated when requested)
  user?: {
    id: string;
    name: string;
  } | null;
  case?: {
    id: string;
    title: string;
  } | null;
  relatedActivities?: Activity[];
  feedbackCount?: number;
  hasUserFeedback?: boolean;
}

export interface CreateActivityDto {
  activityType: string;
  title: string;
  description?: string | null;
  userId?: string | null;
  caseId?: string | null;
  data?: Record<string, unknown> | null;
}

export interface UpdateActivityDto {
  id: string;
  title?: string;
  description?: string | null;
  data?: Record<string, unknown> | null;
}

export interface ActivityFeedback {
  id: string;
  activityId: string;
  userId: string;
  reaction: string;
  comment: string | null;
  createdAt: Date;
}

export interface CreateActivityFeedbackDto {
  activityId: string;
  userId: string;
  reaction: string;
  comment?: string | null;
}

// Validation schemas
// Define the schema type to fix recursive reference issues
type ActivitySchemaType = z.ZodObject<{
  id: z.ZodString;
  activityType: z.ZodString;
  title: z.ZodString;
  description: z.ZodNullable<z.ZodString>;
  userId: z.ZodNullable<z.ZodString>;
  caseId: z.ZodNullable<z.ZodString>;
  tenantId: z.ZodString;
  data: z.ZodNullable<z.ZodRecord>;
  createdAt: z.ZodNullable<z.ZodDate>;
  updatedAt: z.ZodNullable<z.ZodDate>;
  user: z.ZodOptional<z.ZodNullable<z.ZodObject<{
    id: z.ZodString;
    name: z.ZodString;
  }>>>;
  case: z.ZodOptional<z.ZodNullable<z.ZodObject<{
    id: z.ZodString;
    title: z.ZodString;
  }>>>;
  relatedActivities: z.ZodOptional<z.ZodArray<z.ZodLazy<any>>>;
  feedbackCount: z.ZodOptional<z.ZodNumber>;
  hasUserFeedback: z.ZodOptional<z.ZodBoolean>;
}>;

export const ActivitySchema: ActivitySchemaType = z.object({
  id: z.string(),
  activityType: z.string(),
  title: z.string(),
  description: z.string().nullable(),
  userId: z.string().nullable(),
  caseId: z.string().nullable(),
  tenantId: z.string(),
  data: z.record(z.any()).nullable(),
  createdAt: z.date().nullable(),
  updatedAt: z.date().nullable(),
  user: z.object({
    id: z.string(),
    name: z.string()
  }).nullable().optional(),
  case: z.object({
    id: z.string(),
    title: z.string()
  }).nullable().optional(),
  relatedActivities: z.array(z.lazy<ActivitySchemaType>(() => ActivitySchema)).optional(),
  feedbackCount: z.number().optional(),
  hasUserFeedback: z.boolean().optional()
});

export const CreateActivitySchema = z.object({
  activityType: z.string(),
  title: z.string(),
  description: z.string().nullable().optional(),
  userId: z.string().nullable().optional(),
  caseId: z.string().nullable().optional(),
  data: z.record(z.any()).nullable().optional()
});

export const UpdateActivitySchema = z.object({
  id: z.string(),
  title: z.string().optional(),
  description: z.string().nullable().optional(),
  data: z.record(z.any()).nullable().optional()
});

export const ActivityFeedbackSchema = z.object({
  id: z.string(),
  activityId: z.string(),
  userId: z.string(),
  reaction: z.string(),
  comment: z.string().nullable(),
  createdAt: z.date()
});

export const CreateActivityFeedbackSchema = z.object({
  activityId: z.string(),
  userId: z.string(),
  reaction: z.string(),
  comment: z.string().nullable().optional()
});

// Type definitions for validation
export type ActivityInput = z.infer<typeof ActivitySchema>;
export type CreateActivityInput = z.infer<typeof CreateActivitySchema>;
export type UpdateActivityInput = z.infer<typeof UpdateActivitySchema>;
export type ActivityFeedbackInput = z.infer<typeof ActivityFeedbackSchema>;
export type CreateActivityFeedbackInput = z.infer<typeof CreateActivityFeedbackSchema>;

/**
 * Map a Neo4j record to a domain Activity model
 */
export function mapNeo4jRecordToActivity(record: Neo4jRecord): Activity {
  const activityNode = record.get('activity');
  if (!activityNode) {
    throw new Error('Activity node not found in Neo4j record');
  }

  const activity: Activity = {
    id: activityNode.identity.toString(),
    activityType: activityNode.properties.activity_type,
    title: activityNode.properties.title,
    description: activityNode.properties.description || null,
    userId: activityNode.properties.user_id || null,
    caseId: activityNode.properties.case_id || null,
    tenantId: activityNode.properties.tenant_id,
    data: activityNode.properties.data || null,
    createdAt: activityNode.properties.created_at ? new Date(activityNode.properties.created_at) : null,
    updatedAt: activityNode.properties.updated_at ? new Date(activityNode.properties.updated_at) : null
  };

  // Add relations if they exist in the record
  if (record.has('user')) {
    const userNode = record.get('user');
    if (userNode) {
      activity.user = {
        id: userNode.properties.id,
        name: `${userNode.properties.first_name || ''} ${userNode.properties.last_name || ''}`.trim()
      };
    }
  }

  if (record.has('case')) {
    const caseNode = record.get('case');
    if (caseNode) {
      activity.case = {
        id: caseNode.properties.id,
        title: caseNode.properties.title
      };
    }
  }

  if (record.has('feedbackCount')) {
    activity.feedbackCount = record.get('feedbackCount').toNumber();
  }

  if (record.has('hasUserFeedback')) {
    activity.hasUserFeedback = record.get('hasUserFeedback');
  }

  return activity;
}

/**
 * Map a Neo4j record to a domain ActivityFeedback model
 */
export function mapNeo4jRecordToActivityFeedback(record: Neo4jRecord): ActivityFeedback {
  const feedbackNode = record.get('feedback');
  if (!feedbackNode) {
    throw new Error('Feedback node not found in Neo4j record');
  }

  return {
    id: feedbackNode.identity.toString(),
    activityId: feedbackNode.properties.activity_id,
    userId: feedbackNode.properties.user_id,
    reaction: feedbackNode.properties.reaction,
    comment: feedbackNode.properties.comment || null,
    createdAt: new Date(feedbackNode.properties.created_at)
  };
}

/**
 * Validation utilities for activities
 */
export const ActivityValidation = {
  isUserActivity: (activity: Activity): boolean => {
    return activity.userId !== null;
  },

  isCaseActivity: (activity: Activity): boolean => {
    return activity.caseId !== null;
  },

  isOfType: (activity: Activity, type: ActivityType): boolean => {
    return activity.activityType === type;
  },

  isToday: (activity: Activity): boolean => {
    if (!activity.createdAt) return false;

    const today = new Date();
    const activityDate = new Date(activity.createdAt);

    return today.getFullYear() === activityDate.getFullYear() &&
           today.getMonth() === activityDate.getMonth() &&
           today.getDate() === activityDate.getDate();
  },

  isWithinDays: (activity: Activity, days: number): boolean => {
    if (!activity.createdAt) return false;

    const now = new Date();
    const activityDate = new Date(activity.createdAt);
    const diffTime = Math.abs(now.getTime() - activityDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays <= days;
  }
};

/**
 * Repository implementation for activities
 */
export class ActivityRepository {
  private static driver: Driver | null = null;

  private static getSession() {
    // Initialize driver if not already done
    if (!this.driver) {
      // Cast the result of getDriver to Driver since we know it will return a Driver
      this.driver = getDriver() as Driver;
    }
    return this.driver.session();
  }

  /**
   * Get all activities for a tenant
   */
  static async getAllActivities(
    tenantId: string,
    limit: number = 50,
    offset: number = 0,
    includeRelations: boolean = false
  ): Promise<Activity[]> {
    const session = this.getSession();

    try {
      let query = `
        MATCH (activity:Activity)
        WHERE activity.tenant_id = $tenantId
      `;

      if (includeRelations) {
        query += `
          OPTIONAL MATCH (activity)<-[:PERFORMED]-(user:User)
          OPTIONAL MATCH (activity)-[:RELATED_TO]->(case:Case)
          OPTIONAL MATCH (activity)<-[:FEEDBACK_FOR]-(feedback:ActivityFeedback)
          WITH activity, user, case, COUNT(feedback) as feedbackCount
        `;
      } else {
        query += `WITH activity`;
      }

      query += `
        RETURN activity, user, case, feedbackCount
        ORDER BY activity.created_at DESC
        SKIP $offset
        LIMIT $limit
      `;

      const result = await session.run(query, {
        tenantId,
        limit: neo4j.int(limit),
        offset: neo4j.int(offset)
      });

      return result.records.map(record => mapNeo4jRecordToActivity(record));
    } catch (error) {
      console.error('Error fetching activities:', error);
      return [];
    } finally {
      await session.close();
    }
  }

  /**
   * Get an activity by ID
   */
  static async getActivityById(
    id: string,
    tenantId: string,
    includeRelations: boolean = false
  ): Promise<Activity | null> {
    const session = this.getSession();

    try {
      let query = `
        MATCH (activity:Activity)
        WHERE ID(activity) = $id AND activity.tenant_id = $tenantId
      `;

      if (includeRelations) {
        query += `
          OPTIONAL MATCH (activity)<-[:PERFORMED]-(user:User)
          OPTIONAL MATCH (activity)-[:RELATED_TO]->(case:Case)
          OPTIONAL MATCH (activity)<-[:FEEDBACK_FOR]-(feedback:ActivityFeedback)
          WITH activity, user, case, COUNT(feedback) as feedbackCount
        `;
      } else {
        query += `WITH activity`;
      }

      query += `
        RETURN activity, user, case, feedbackCount
      `;

      const result = await session.run(query, {
        id: neo4j.int(id),
        tenantId
      });

      if (result.records.length === 0) {
        return null;
      }

      return mapNeo4jRecordToActivity(result.records[0]);
    } catch (error) {
      console.error('Error fetching activity by ID:', error);
      return null;
    } finally {
      await session.close();
    }
  }

  /**
   * Get activities by user ID
   */
  static async getActivitiesByUser(
    userId: string,
    tenantId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<Activity[]> {
    const session = this.getSession();

    try {
      const query = `
        MATCH (activity:Activity)<-[:PERFORMED]-(user:User)
        WHERE activity.tenant_id = $tenantId AND user.id = $userId
        OPTIONAL MATCH (activity)-[:RELATED_TO]->(case:Case)
        OPTIONAL MATCH (activity)<-[:FEEDBACK_FOR]-(feedback:ActivityFeedback)
        WITH activity, user, case, COUNT(feedback) as feedbackCount
        RETURN activity, user, case, feedbackCount
        ORDER BY activity.created_at DESC
        SKIP $offset
        LIMIT $limit
      `;

      const result = await session.run(query, {
        userId,
        tenantId,
        limit: neo4j.int(limit),
        offset: neo4j.int(offset)
      });

      return result.records.map(record => mapNeo4jRecordToActivity(record));
    } catch (error) {
      console.error('Error fetching user activities:', error);
      return [];
    } finally {
      await session.close();
    }
  }

  /**
   * Get activities by case ID
   */
  static async getActivitiesByCase(
    caseId: string,
    tenantId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<Activity[]> {
    const session = this.getSession();

    try {
      const query = `
        MATCH (activity:Activity)-[:RELATED_TO]->(case:Case)
        WHERE activity.tenant_id = $tenantId AND case.id = $caseId
        OPTIONAL MATCH (activity)<-[:PERFORMED]-(user:User)
        OPTIONAL MATCH (activity)<-[:FEEDBACK_FOR]-(feedback:ActivityFeedback)
        WITH activity, user, case, COUNT(feedback) as feedbackCount
        RETURN activity, user, case, feedbackCount
        ORDER BY activity.created_at DESC
        SKIP $offset
        LIMIT $limit
      `;

      const result = await session.run(query, {
        caseId,
        tenantId,
        limit: neo4j.int(limit),
        offset: neo4j.int(offset)
      });

      return result.records.map(record => mapNeo4jRecordToActivity(record));
    } catch (error) {
      console.error('Error fetching case activities:', error);
      return [];
    } finally {
      await session.close();
    }
  }

  /**
   * Get activities by type
   */
  static async getActivitiesByType(
    activityType: string,
    tenantId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<Activity[]> {
    const session = this.getSession();

    try {
      const query = `
        MATCH (activity:Activity)
        WHERE activity.tenant_id = $tenantId AND activity.activity_type = $activityType
        OPTIONAL MATCH (activity)<-[:PERFORMED]-(user:User)
        OPTIONAL MATCH (activity)-[:RELATED_TO]->(case:Case)
        OPTIONAL MATCH (activity)<-[:FEEDBACK_FOR]-(feedback:ActivityFeedback)
        WITH activity, user, case, COUNT(feedback) as feedbackCount
        RETURN activity, user, case, feedbackCount
        ORDER BY activity.created_at DESC
        SKIP $offset
        LIMIT $limit
      `;

      const result = await session.run(query, {
        activityType,
        tenantId,
        limit: neo4j.int(limit),
        offset: neo4j.int(offset)
      });

      return result.records.map(record => mapNeo4jRecordToActivity(record));
    } catch (error) {
      console.error(`Error fetching ${activityType} activities:`, error);
      return [];
    } finally {
      await session.close();
    }
  }

  /**
   * Create a new activity
   */
  static async createActivity(
    activityData: CreateActivityDto,
    tenantId: string
  ): Promise<Activity | null> {
    // Validate input
    const validationResult = CreateActivitySchema.safeParse(activityData);
    if (!validationResult.success) {
      console.error('Activity validation failed:', validationResult.error);
      throw new Error(`Activity validation failed: ${validationResult.error.message}`);
    }

    const session = this.getSession();

    try {
      const now = new Date().toISOString();
      let query = `
        CREATE (activity:Activity {
          activity_type: $activityType,
          title: $title,
          description: $description,
          tenant_id: $tenantId,
          data: $data,
          created_at: $createdAt,
          updated_at: $updatedAt
        })
      `;

      const params: Record<string, unknown> = {
        activityType: activityData.activityType,
        title: activityData.title,
        description: activityData.description || null,
        tenantId,
        data: activityData.data || null,
        createdAt: now,
        updatedAt: now
      };

      // Add user relationship if userId is provided
      if (activityData.userId) {
        query += `
          WITH activity
          MATCH (user:User {id: $userId, tenant_id: $tenantId})
          CREATE (user)-[:PERFORMED]->(activity)
          SET activity.user_id = $userId
        `;
        params.userId = activityData.userId;
      }

      // Add case relationship if caseId is provided
      if (activityData.caseId) {
        query += `
          WITH activity
          MATCH (case:Case {id: $caseId, tenant_id: $tenantId})
          CREATE (activity)-[:RELATED_TO]->(case)
          SET activity.case_id = $caseId
        `;
        params.caseId = activityData.caseId;
      }

      // Return the created activity
      query += `
        WITH activity
        OPTIONAL MATCH (activity)<-[:PERFORMED]-(user:User)
        OPTIONAL MATCH (activity)-[:RELATED_TO]->(case:Case)
        RETURN activity, user, case, 0 as feedbackCount
      `;

      const result = await session.run(query, params);

      if (result.records.length === 0) {
        return null;
      }

      return mapNeo4jRecordToActivity(result.records[0]);
    } catch (error) {
      console.error('Error creating activity:', error);
      throw error;
    } finally {
      await session.close();
    }
  }

  /**
   * Update an existing activity
   */
  static async updateActivity(
    activityData: UpdateActivityDto,
    tenantId: string
  ): Promise<Activity | null> {
    // Validate input
    const validationResult = UpdateActivitySchema.safeParse(activityData);
    if (!validationResult.success) {
      console.error('Activity update validation failed:', validationResult.error);
      throw new Error(`Activity update validation failed: ${validationResult.error.message}`);
    }

    const session = this.getSession();

    try {
      let setClause = 'SET activity.updated_at = $updatedAt';
      const params: Record<string, unknown> = {
        id: neo4j.int(activityData.id),
        tenantId,
        updatedAt: new Date().toISOString()
      };

      if (activityData.title !== undefined) {
        setClause += ', activity.title = $title';
        params.title = activityData.title;
      }

      if (activityData.description !== undefined) {
        setClause += ', activity.description = $description';
        params.description = activityData.description;
      }

      if (activityData.data !== undefined) {
        setClause += ', activity.data = $data';
        params.data = activityData.data;
      }

      const query = `
        MATCH (activity:Activity)
        WHERE ID(activity) = $id AND activity.tenant_id = $tenantId
        ${setClause}
        WITH activity
        OPTIONAL MATCH (activity)<-[:PERFORMED]-(user:User)
        OPTIONAL MATCH (activity)-[:RELATED_TO]->(case:Case)
        OPTIONAL MATCH (activity)<-[:FEEDBACK_FOR]-(feedback:ActivityFeedback)
        WITH activity, user, case, COUNT(feedback) as feedbackCount
        RETURN activity, user, case, feedbackCount
      `;

      const result = await session.run(query, params);

      if (result.records.length === 0) {
        return null;
      }

      return mapNeo4jRecordToActivity(result.records[0]);
    } catch (error) {
      console.error('Error updating activity:', error);
      return null;
    } finally {
      await session.close();
    }
  }

  /**
   * Delete an activity
   */
  static async deleteActivity(
    id: string,
    tenantId: string
  ): Promise<boolean> {
    const session = this.getSession();

    try {
      // Delete activity and related feedback
      const query = `
        MATCH (activity:Activity)
        WHERE ID(activity) = $id AND activity.tenant_id = $tenantId
        OPTIONAL MATCH (activity)<-[r:PERFORMED]-()
        OPTIONAL MATCH (activity)-[r2:RELATED_TO]-()
        OPTIONAL MATCH (activity)<-[r3:FEEDBACK_FOR]-(feedback:ActivityFeedback)
        DETACH DELETE feedback, activity
      `;

      await session.run(query, {
        id: neo4j.int(id),
        tenantId
      });

      return true;
    } catch (error) {
      console.error('Error deleting activity:', error);
      return false;
    } finally {
      await session.close();
    }
  }

  /**
   * Add feedback to an activity
   */
  static async addActivityFeedback(
    feedbackData: CreateActivityFeedbackDto,
    tenantId: string
  ): Promise<ActivityFeedback | null> {
    // Validate input
    const validationResult = CreateActivityFeedbackSchema.safeParse(feedbackData);
    if (!validationResult.success) {
      console.error('Activity feedback validation failed:', validationResult.error);
      throw new Error(`Activity feedback validation failed: ${validationResult.error.message}`);
    }

    const session = this.getSession();

    try {
      const query = `
        MATCH (activity:Activity)
        WHERE ID(activity) = $activityId AND activity.tenant_id = $tenantId
        MATCH (user:User {id: $userId, tenant_id: $tenantId})
        CREATE (feedback:ActivityFeedback {
          activity_id: $activityId,
          user_id: $userId,
          reaction: $reaction,
          comment: $comment,
          created_at: $createdAt
        })
        CREATE (feedback)-[:FEEDBACK_FOR]->(activity)
        CREATE (user)-[:GAVE_FEEDBACK]->(feedback)
        RETURN feedback
      `;

      const result = await session.run(query, {
        activityId: neo4j.int(feedbackData.activityId),
        userId: feedbackData.userId,
        tenantId,
        reaction: feedbackData.reaction,
        comment: feedbackData.comment || null,
        createdAt: new Date().toISOString()
      });

      if (result.records.length === 0) {
        return null;
      }

      return mapNeo4jRecordToActivityFeedback(result.records[0]);
    } catch (error) {
      console.error('Error adding activity feedback:', error);
      return null;
    } finally {
      await session.close();
    }
  }

  /**
   * Get all feedback for an activity
   */
  static async getActivityFeedback(
    activityId: string,
    tenantId: string
  ): Promise<ActivityFeedback[]> {
    const session = this.getSession();

    try {
      const query = `
        MATCH (activity:Activity)<-[:FEEDBACK_FOR]-(feedback:ActivityFeedback)
        WHERE ID(activity) = $activityId AND activity.tenant_id = $tenantId
        RETURN feedback
        ORDER BY feedback.created_at DESC
      `;

      const result = await session.run(query, {
        activityId: neo4j.int(activityId),
        tenantId
      });

      return result.records.map(record => mapNeo4jRecordToActivityFeedback(record));
    } catch (error) {
      console.error('Error fetching activity feedback:', error);
      return [];
    } finally {
      await session.close();
    }
  }

  /**
   * Get related activities for an activity
   */
  static async getRelatedActivities(
    activityId: string,
    tenantId: string,
    limit: number = 5
  ): Promise<Activity[]> {
    const session = this.getSession();

    try {
      // Find activities that are related to the same case or created by the same user
      const query = `
        MATCH (activity:Activity)
        WHERE ID(activity) = $activityId AND activity.tenant_id = $tenantId
        OPTIONAL MATCH (activity)-[:RELATED_TO]->(case:Case)
        OPTIONAL MATCH (activity)<-[:PERFORMED]-(user:User)
        WITH activity, case, user
        WHERE case IS NOT NULL OR user IS NOT NULL

        MATCH (relatedActivity:Activity)
        WHERE relatedActivity.tenant_id = $tenantId AND ID(relatedActivity) <> $activityId
        AND (
          (case IS NOT NULL AND (relatedActivity)-[:RELATED_TO]->(case))
          OR
          (user IS NOT NULL AND (relatedActivity)<-[:PERFORMED]-(user))
        )

        WITH relatedActivity
        OPTIONAL MATCH (relatedActivity)<-[:PERFORMED]-(relUser:User)
        OPTIONAL MATCH (relatedActivity)-[:RELATED_TO]->(relCase:Case)
        OPTIONAL MATCH (relatedActivity)<-[:FEEDBACK_FOR]-(feedback:ActivityFeedback)

        WITH relatedActivity, relUser, relCase, COUNT(feedback) as feedbackCount
        RETURN relatedActivity as activity, relUser as user, relCase as case, feedbackCount
        ORDER BY relatedActivity.created_at DESC
        LIMIT $limit
      `;

      const result = await session.run(query, {
        activityId: neo4j.int(activityId),
        tenantId,
        limit: neo4j.int(limit)
      });

      return result.records.map(record => mapNeo4jRecordToActivity(record));
    } catch (error) {
      console.error('Error fetching related activities:', error);
      return [];
    } finally {
      await session.close();
    }
  }

  /**
   * Get activity stream for user's dashboard
   * This combines activities from cases the user is involved with,
   * activities the user created, and high-priority firm-wide activities
   */
  static async getActivityStream(
    userId: string,
    tenantId: string,
    limit: number = 20
  ): Promise<Activity[]> {
    const session = this.getSession();

    try {
      const query = `
        // User's own activities
        MATCH (user:User {id: $userId, tenant_id: $tenantId})-[:PERFORMED]->(userActivity:Activity)
        WHERE userActivity.tenant_id = $tenantId

        WITH userActivity

        // Activities from cases the user is involved with
        MATCH (caseUser:User {id: $userId, tenant_id: $tenantId})-[:INVOLVED_WITH]->(userCase:Case)
        MATCH (caseActivity:Activity)-[:RELATED_TO]->(userCase)
        WHERE caseActivity.tenant_id = $tenantId

        WITH COLLECT(userActivity) + COLLECT(caseActivity) AS allActivities

        // High-priority firm-wide activities
        MATCH (firmActivity:Activity)
        WHERE firmActivity.tenant_id = $tenantId
        AND firmActivity.activity_type IN ['case_created', 'deadline_missed', 'insight_generated']
        AND NOT firmActivity IN allActivities

        WITH allActivities + COLLECT(firmActivity) AS combinedActivities
        UNWIND combinedActivities AS activity

        // Get relations for activities
        OPTIONAL MATCH (activity)<-[:PERFORMED]-(activityUser:User)
        OPTIONAL MATCH (activity)-[:RELATED_TO]->(activityCase:Case)
        OPTIONAL MATCH (activity)<-[:FEEDBACK_FOR]-(feedback:ActivityFeedback)
        OPTIONAL MATCH (feedback)<-[:GAVE_FEEDBACK]-(feedbackUser:User {id: $userId})

        WITH DISTINCT activity, activityUser as user, activityCase as case,
             COUNT(feedback) as feedbackCount,
             COUNT(feedbackUser) > 0 as hasUserFeedback

        RETURN activity, user, case, feedbackCount, hasUserFeedback
        ORDER BY activity.created_at DESC
        LIMIT $limit
      `;

      const result = await session.run(query, {
        userId,
        tenantId,
        limit: neo4j.int(limit)
      });

      return result.records.map(record => mapNeo4jRecordToActivity(record));
    } catch (error) {
      console.error('Error fetching activity stream:', error);
      return [];
    } finally {
      await session.close();
    }
  }
}

/**
 * Template Data Mapper
 *
 * Maps between database records and domain models for both global legal templates (public schema)
 * and tenant-specific authored document templates (tenants schema).
 */

import { Database, Json } from '@/lib/supabase/database.types';
import { createClient } from '@/lib/supabase/server';
import { PostgrestFilterBuilder } from '@supabase/postgrest-js';
import { z } from 'zod';
import { toJsonSafe, fromJsonSafe, mapMetadata } from '@/lib/utils/type-converters';

/**
 * Helper function to apply conditional filtering to queries
 * Replaces the non-existent conditionalFilter method
 */
function applyConditionalFilter<T, K>(
  query: PostgrestFilterBuilder<any, any, T[], K>,
  column: string,
  condition: boolean,
  operator: string | undefined,
  value: any
): PostgrestFilterBuilder<any, any, T[], K> {
  if (condition && operator) {
    return query.eq(column, value);
  }
  return query;
}

// Enums
export enum TemplateCategory {
  PLEADING = 'pleading',
  DISCOVERY = 'discovery',
  CORRESPONDENCE = 'correspondence',
  SETTLEMENT = 'settlement',
  CONTRACT = 'contract',
  OTHER = 'other'
}

export enum TemplateSource {
  GLOBAL = 'global',  // from public.legal_templates
  TENANT = 'tenant'   // from tenants.authored_document_templates
}

// Domain Model Interfaces
export interface Template {
  id: string;
  title: string;
  description: string | null;
  category: string;
  content: string;
  variables: Record<string, unknown> | null;
  isActive: boolean;
  version: number | null;
  createdAt: Date | null;
  updatedAt: Date | null;
  createdBy: string | null;
  updatedBy: string | null;
  tenantId: string | null;
  source: TemplateSource;
  metadata: Record<string, unknown> | null;

  // Global template specific fields
  documentType?: string;
  practiceArea?: string;
  state?: string;
  jurisdiction?: string | null;
  subcategory?: string | null;
  allowsConditionalBlocks?: boolean | null;
  allowsLoops?: boolean | null;
  templateEngine?: string | null;
}

export interface CreateTemplateDto {
  title: string;
  description?: string | null;
  category: string;
  content: string;
  variables?: Record<string, unknown> | null;
  isActive?: boolean;
  metadata?: Record<string, unknown> | null;

  // Global template specific fields
  documentType?: string;
  practiceArea?: string;
  state?: string;
  jurisdiction?: string | null;
  subcategory?: string | null;
  allowsConditionalBlocks?: boolean | null;
  allowsLoops?: boolean | null;
  templateEngine?: string | null;
}

export interface UpdateTemplateDto {
  id: string;
  title?: string;
  description?: string | null;
  category?: string;
  content?: string;
  variables?: Record<string, unknown> | null;
  isActive?: boolean;
  metadata?: Record<string, unknown> | null;

  // Global template specific fields
  documentType?: string;
  practiceArea?: string;
  state?: string;
  jurisdiction?: string | null;
  subcategory?: string | null;
  allowsConditionalBlocks?: boolean | null;
  allowsLoops?: boolean | null;
  templateEngine?: string | null;
}

// Validation schemas
export const TemplateSchema = z.object({
  id: z.string().uuid(),
  title: z.string().min(1),
  description: z.string().nullable(),
  category: z.string().min(1),
  content: z.string().min(1),
  variables: z.record(z.any()).nullable(),
  isActive: z.boolean().default(true),
  version: z.number().nullable(),
  createdAt: z.date().nullable(),
  updatedAt: z.date().nullable(),
  createdBy: z.string().nullable(),
  updatedBy: z.string().nullable(),
  tenantId: z.string().nullable(),
  source: z.enum([TemplateSource.GLOBAL, TemplateSource.TENANT]),
  metadata: z.record(z.any()).nullable(),

  // Optional fields for global templates
  documentType: z.string().optional(),
  practiceArea: z.string().optional(),
  state: z.string().optional(),
  jurisdiction: z.string().nullable().optional(),
  subcategory: z.string().nullable().optional(),
  allowsConditionalBlocks: z.boolean().nullable().optional(),
  allowsLoops: z.boolean().nullable().optional(),
  templateEngine: z.string().nullable().optional(),
});

export const CreateTemplateSchema = z.object({
  title: z.string().min(1),
  description: z.string().nullable().optional(),
  category: z.string().min(1),
  content: z.string().min(1),
  variables: z.record(z.any()).nullable().optional(),
  isActive: z.boolean().optional().default(true),
  metadata: z.record(z.any()).nullable().optional(),

  // Optional fields for global templates
  documentType: z.string().optional(),
  practiceArea: z.string().optional(),
  state: z.string().optional(),
  jurisdiction: z.string().nullable().optional(),
  subcategory: z.string().nullable().optional(),
  allowsConditionalBlocks: z.boolean().nullable().optional(),
  allowsLoops: z.boolean().nullable().optional(),
  templateEngine: z.string().nullable().optional(),
});

export const UpdateTemplateSchema = z.object({
  id: z.string().uuid(),
  title: z.string().min(1).optional(),
  description: z.string().nullable().optional(),
  category: z.string().min(1).optional(),
  content: z.string().min(1).optional(),
  variables: z.record(z.any()).nullable().optional(),
  isActive: z.boolean().optional(),
  metadata: z.record(z.any()).nullable().optional(),

  // Optional fields for global templates
  documentType: z.string().optional(),
  practiceArea: z.string().optional(),
  state: z.string().optional(),
  jurisdiction: z.string().nullable().optional(),
  subcategory: z.string().nullable().optional(),
  allowsConditionalBlocks: z.boolean().nullable().optional(),
  allowsLoops: z.boolean().nullable().optional(),
  templateEngine: z.string().nullable().optional(),
});

// Type definitions for validation
export type TemplateInput = z.infer<typeof TemplateSchema>;
export type CreateTemplateInput = z.infer<typeof CreateTemplateSchema>;
export type UpdateTemplateInput = z.infer<typeof UpdateTemplateSchema>;

/**
 * Map a global legal template row to a domain Template model
 */
export function mapGlobalTemplateRowToTemplate(
  row: Database['public']['Tables']['legal_templates']['Row']
): Template {
  return {
    id: row.id,
    // Map 'name' field from legal_templates to 'title' in our domain model
    title: row.name,
    description: row.description,
    category: row.category,
    // Convert Json content to string for domain model
    content: typeof row.content === 'string' ? row.content : JSON.stringify(row.content),
    variables: fromJsonSafe(row.variables as Json) || null,
    isActive: row.is_active ?? true,
    version: row.version,
    createdAt: row.created_at ? new Date(row.created_at) : null,
    updatedAt: row.updated_at ? new Date(row.updated_at) : null,
    createdBy: row.created_by,
    // No updated_by in schema, use null
    updatedBy: null,
    tenantId: row.tenant_id, // Global templates do have tenant_id in schema
    source: TemplateSource.GLOBAL,
    // No metadata in global templates, use null
    metadata: null,

    // Global template specific fields
    documentType: row.document_type ?? undefined,
    practiceArea: row.practice_area ?? undefined,
    state: row.state ?? undefined,
    jurisdiction: row.jurisdiction,
    subcategory: row.subcategory,
    allowsConditionalBlocks: row.allows_conditional_blocks,
    allowsLoops: row.allows_loops,
    templateEngine: row.template_engine,
  };
}

/**
 * Map a tenant-specific template row to a domain Template model
 */
export function mapTenantTemplateRowToTemplate(
  row: Database['tenants']['Tables']['authored_document_templates']['Row']
): Template {
  return {
    id: row.id,
    title: row.title as unknown as string,
    description: row.description,
    category: row.category,
    content: String(row.content),
    variables: fromJsonSafe(row.variables as Json) || null,
    isActive: row.is_active ?? true,
    version: row.version,
    createdAt: row.created_at ? new Date(row.created_at) : null,
    updatedAt: row.updated_at ? new Date(row.updated_at) : null,
    createdBy: row.created_by,
    updatedBy: row.updated_at ? row.created_by : null, // Use created_by as fallback
    tenantId: row.tenant_id,
    source: TemplateSource.TENANT,
    metadata: mapMetadata(row, 'metadata') || null,
  };
}

/**
 * Map a create template DTO to a global legal template insert object
 */
export function mapCreateTemplateDtoToGlobalTemplateInsert(
  dto: CreateTemplateDto,
  tenantId: string,
  userId: string
): Database['public']['Tables']['legal_templates']['Insert'] {
  return {
    // Map title from DTO to name in database schema
    name: dto.title,
    description: dto.description ?? null,
    category: dto.category,
    // Content is Json in database schema, convert string to JSON object
    content: { content: dto.content } as unknown as Json,
    variables: toJsonSafe(dto.variables || null) as Json,
    is_active: dto.isActive !== undefined ? dto.isActive : true,
    tenant_id: tenantId,
    created_by: userId,
    // No metadata in global templates schema
    // No updated_by in schema

    // Global template specific fields
    document_type: dto.documentType || '',
    practice_area: dto.practiceArea || '',
    state: dto.state || '',
    jurisdiction: dto.jurisdiction ?? null,
    subcategory: dto.subcategory ?? null,
    allows_conditional_blocks: dto.allowsConditionalBlocks ?? null,
    allows_loops: dto.allowsLoops ?? null,
    template_engine: dto.templateEngine ?? null,
  };
}

/**
 * Map a create template DTO to a tenant template insert object
 */
export function mapCreateTemplateDtoToTenantTemplateInsert(
  dto: CreateTemplateDto,
  tenantId: string,
  userId: string
): Database['tenants']['Tables']['authored_document_templates']['Insert'] {
  return {
    title: dto.title as string,
    description: dto.description ?? null,
    category: dto.category,
    // Content should be a string directly, not a Json object
    content: dto.content,
    variables: toJsonSafe(dto.variables || null) as Json,
    is_active: dto.isActive !== undefined ? dto.isActive : true,
    tenant_id: tenantId,
    created_by: userId,
    updated_by: userId,
    metadata: toJsonSafe(dto.metadata || null) as Json,
  };
}

/**
 * Map an update template DTO to a global template update object
 */
export function mapUpdateTemplateDtoToGlobalTemplateUpdate(
  dto: UpdateTemplateDto,
  userId: string
): Database['public']['Tables']['legal_templates']['Update'] {
  // Create update object with only valid properties based on schema
  const update: Database['public']['Tables']['legal_templates']['Update'] = {
    updated_at: new Date().toISOString(),
  };

  // Map title from DTO to name in database schema
  if (dto.title !== undefined) update.name = dto.title;
  if (dto.description !== undefined) update.description = dto.description;
  if (dto.category !== undefined) update.category = dto.category;
  // Content is Json in database schema
  if (dto.content !== undefined) update.content = { content: dto.content } as unknown as Json;
  if (dto.variables !== undefined) update.variables = toJsonSafe(dto.variables || null) as Json;
  if (dto.isActive !== undefined) update.is_active = dto.isActive;
  // No metadata field in global templates schema

  // Global template specific fields
  if (dto.documentType !== undefined) update.document_type = dto.documentType || '';
  if (dto.practiceArea !== undefined) update.practice_area = dto.practiceArea || '';
  if (dto.state !== undefined) update.state = dto.state || '';
  if (dto.jurisdiction !== undefined) update.jurisdiction = dto.jurisdiction;
  if (dto.subcategory !== undefined) update.subcategory = dto.subcategory;
  if (dto.allowsConditionalBlocks !== undefined) update.allows_conditional_blocks = dto.allowsConditionalBlocks;
  if (dto.allowsLoops !== undefined) update.allows_loops = dto.allowsLoops;
  if (dto.templateEngine !== undefined) update.template_engine = dto.templateEngine;

  return update;
}

/**
 * Map an update template DTO to a tenant template update object
 */
export function mapUpdateTemplateDtoToTenantTemplateUpdate(
  dto: UpdateTemplateDto,
  userId: string
): Database['tenants']['Tables']['authored_document_templates']['Update'] {
  // Create update object with only valid properties based on schema
  const update: Database['tenants']['Tables']['authored_document_templates']['Update'] = {
    // Include only fields that exist in the schema
    updated_at: new Date().toISOString(),
  };

  // Apply type casting to ensure TypeScript recognizes these fields
  if (dto.title !== undefined) update.title = dto.title as string;
  if (dto.description !== undefined) update.description = dto.description;
  if (dto.category !== undefined) update.category = dto.category;
  if (dto.content !== undefined) update.content = dto.content;
  if (dto.variables !== undefined) update.variables = toJsonSafe(dto.variables || null) as Json;
  if (dto.isActive !== undefined) update.is_active = dto.isActive;
  if (dto.metadata !== undefined) update.metadata = toJsonSafe(dto.metadata || null) as Json;

  return update;
}

/**
 * Validation utilities for templates
 */
export const TemplateValidation = {
  isActive(template: Template): boolean {
    return template.isActive === true;
  },

  hasVariables(template: Template): boolean {
    return template.variables !== null && Object.keys(template.variables).length > 0;
  },

  extractVariables(content: string): string[] {
    const regex = /\{\{([^}]+)\}\}/g;
    const matches = content.match(regex) || [];
    return matches.map(match => match.replace(/\{\{|\}\}/g, '').trim());
  }
};

/**
 * Template Service for managing templates from different sources
 */
export class TemplateService {
  /**
   * Get all templates for a tenant
   */
  static async getTemplates(
    tenantId: string,
    includeGlobal: boolean = true,
    isActiveOnly: boolean = true
  ): Promise<Template[]> {
    const supabase = createClient();
    const templates: Template[] = [];

    try {
      // Get tenant-specific templates
      let query = supabase
        .from('authored_document_templates')
        .select('*')
        .eq('tenant_id', tenantId);

      // Apply conditional filter
      if (isActiveOnly) {
        query = query.eq('is_active', true);
      }

      const { data: tenantTemplates, error: tenantError } = await query;

      if (tenantError) throw tenantError;

      if (tenantTemplates) {
        templates.push(
          ...tenantTemplates.map((row: Database['tenants']['Tables']['authored_document_templates']['Row']) =>
            mapTenantTemplateRowToTemplate(row)
          )
        );
      }

      // Get global templates if requested
      if (includeGlobal) {
        let globalQuery = supabase
          .from('legal_templates')
          .select('*');

        // Apply conditional filter
        if (isActiveOnly) {
          globalQuery = globalQuery.eq('is_active', true);
        }

        const { data: globalTemplates, error: globalError } = await globalQuery;

        if (globalError) throw globalError;

        if (globalTemplates) {
          templates.push(
            ...globalTemplates.map((row: Database['public']['Tables']['legal_templates']['Row']) =>
              mapGlobalTemplateRowToTemplate(row)
            )
          );
        }
      }

      return templates;
    } catch (error) {
      console.error('Error fetching templates:', error);
      throw error;
    }
  }

  /**
   * Get a specific template by ID
   */
  static async getTemplateById(
    templateId: string,
    tenantId: string
  ): Promise<Template | null> {
    const supabase = createClient();

    try {
      // First try to find in tenant templates
      const { data: tenantTemplate, error: tenantError } = await supabase
        .from('authored_document_templates')
        .select('*')
        .eq('id', templateId)
        .eq('tenant_id', tenantId)
        .single();

      if (tenantTemplate) {
        return mapTenantTemplateRowToTemplate(
          tenantTemplate as Database['tenants']['Tables']['authored_document_templates']['Row']
        );
      }

      // If not found, try to find in global templates
      const { data: globalTemplate, error: globalError } = await supabase
        .from('legal_templates')
        .select('*')
        .eq('id', templateId)
        .single();

      if (globalTemplate) {
        return mapGlobalTemplateRowToTemplate(
          globalTemplate as Database['public']['Tables']['legal_templates']['Row']
        );
      }

      return null;
    } catch (error) {
      console.error('Error fetching template:', error);
      return null;
    }
  }

  /**
   * Search for templates by various criteria
   */
  static async searchTemplates(
    params: {
      tenantId: string;
      searchTerm?: string;
      category?: string;
      documentType?: string;
      practiceArea?: string;
      state?: string;
      includeGlobal?: boolean;
      isActiveOnly?: boolean;
    }
  ): Promise<Template[]> {
    const {
      tenantId,
      searchTerm,
      category,
      documentType,
      practiceArea,
      state,
      includeGlobal = true,
      isActiveOnly = true
    } = params;

    const supabase = createClient();
    const templates: Template[] = [];

    try {
      // Helper function to build filters
      const buildTenantFilters = (query: any) => {
        let result = query.eq('tenant_id', tenantId);
        if (isActiveOnly) result = result.eq('is_active', true);
        if (category) result = result.eq('category', category);
        if (searchTerm) result = result.ilike('title', `%${searchTerm}%`);
        return result;
      };

      const buildGlobalFilters = (query: any) => {
        let result = query;
        if (isActiveOnly) result = result.eq('is_active', true);
        if (category) result = result.eq('category', category);
        if (documentType) result = result.eq('document_type', documentType);
        if (practiceArea) result = result.eq('practice_area', practiceArea);
        if (state) result = result.eq('state', state);
        if (searchTerm) result = result.ilike('title', `%${searchTerm}%`);
        return result;
      };

      // Get tenant templates
      const { data: tenantTemplates, error: tenantError } = await buildTenantFilters(
        supabase.from('authored_document_templates').select('*')
      );

      if (tenantError) throw tenantError;

      if (tenantTemplates) {
        templates.push(
          ...tenantTemplates.map((row: Database['tenants']['Tables']['authored_document_templates']['Row']) =>
            mapTenantTemplateRowToTemplate(row)
          )
        );
      }

      // Get global templates if requested
      if (includeGlobal) {
        const { data: globalTemplates, error: globalError } = await buildGlobalFilters(
          supabase.from('legal_templates').select('*')
        );

        if (globalError) throw globalError;

        if (globalTemplates) {
          templates.push(
            ...globalTemplates.map((row: Database['public']['Tables']['legal_templates']['Row']) =>
              mapGlobalTemplateRowToTemplate(row)
            )
          );
        }
      }

      return templates;
    } catch (error) {
      console.error('Error searching templates:', error);
      throw error;
    }
  }

  /**
   * Create a new template
   */
  static async createTemplate(
    templateData: CreateTemplateDto,
    tenantId: string,
    userId: string,
    isGlobal: boolean = false
  ): Promise<Template | null> {
    // Validate input
    const validationResult = CreateTemplateSchema.safeParse(templateData);
    if (!validationResult.success) {
      console.error('Template creation validation failed:', validationResult.error);
      throw new Error(`Template creation validation failed: ${validationResult.error.message}`);
    }

    const supabase = createClient();

    try {
      if (isGlobal) {
        // Create global template
        const insert = mapCreateTemplateDtoToGlobalTemplateInsert(templateData, tenantId, userId);
        const { data, error } = await supabase
          .from('legal_templates')
          .insert(insert)
          .select()
          .single();

        if (error) throw error;

        if (data) {
          return mapGlobalTemplateRowToTemplate(
            data as Database['public']['Tables']['legal_templates']['Row']
          );
        }
      } else {
        // Create tenant template
        const insert = mapCreateTemplateDtoToTenantTemplateInsert(templateData, tenantId, userId);
        const { data, error } = await supabase
          .from('authored_document_templates')
          .insert(insert)
          .select()
          .single();

        if (error) throw error;

        if (data) {
          return mapTenantTemplateRowToTemplate(
            data as Database['tenants']['Tables']['authored_document_templates']['Row']
          );
        }
      }

      return null;
    } catch (error) {
      console.error('Error creating template:', error);
      throw error;
    }
  }

  /**
   * Update an existing template
   */
  static async updateTemplate(
    templateData: UpdateTemplateDto,
    tenantId: string,
    userId: string
  ): Promise<Template | null> {
    // Validate input
    const validationResult = UpdateTemplateSchema.safeParse(templateData);
    if (!validationResult.success) {
      console.error('Template update validation failed:', validationResult.error);
      throw new Error(`Template update validation failed: ${validationResult.error.message}`);
    }

    const supabase = createClient();

    try {
      // First determine if this is a global or tenant template
      const existing = await this.getTemplateById(templateData.id, tenantId);
      if (!existing) {
        throw new Error(`Template with ID ${templateData.id} not found`);
      }

      if (existing.source === TemplateSource.GLOBAL) {
        // Update global template
        const update = mapUpdateTemplateDtoToGlobalTemplateUpdate(templateData, userId);
        const { data, error } = await supabase
          .from('legal_templates')
          .update(update)
          .eq('id', templateData.id)
          .select()
          .single();

        if (error) throw error;

        if (data) {
          return mapGlobalTemplateRowToTemplate(
            data as Database['public']['Tables']['legal_templates']['Row']
          );
        }
      } else {
        // Update tenant template
        const update = mapUpdateTemplateDtoToTenantTemplateUpdate(templateData, userId);
        const { data, error } = await supabase
          .from('authored_document_templates')
          .update(update)
          .eq('id', templateData.id)
          .eq('tenant_id', tenantId) // Ensure tenant isolation
          .select()
          .single();

        if (error) throw error;

        if (data) {
          return mapTenantTemplateRowToTemplate(
            data as Database['tenants']['Tables']['authored_document_templates']['Row']
          );
        }
      }

      return null;
    } catch (error) {
      console.error('Error updating template:', error);
      throw error;
    }
  }

  /**
   * Delete a template (or mark as inactive)
   */
  static async deleteTemplate(
    id: string,
    tenantId: string,
    hardDelete: boolean = false
  ): Promise<boolean> {
    const supabase = createClient();

    try {
      // First determine if this is a global or tenant template
      const existing = await this.getTemplateById(id, tenantId);
      if (!existing) {
        throw new Error(`Template with ID ${id} not found`);
      }

      if (existing.source === TemplateSource.GLOBAL) {
        if (hardDelete) {
          // Hard delete global template
          const { error } = await supabase
            .from('legal_templates')
            .delete()
            .eq('id', id);

          if (error) throw error;
        } else {
          // Soft delete global template
          const { error } = await supabase
            .from('legal_templates')
            .update({ is_active: false })
            .eq('id', id);

          if (error) throw error;
        }
      } else {
        if (hardDelete) {
          // Hard delete tenant template
          const { error } = await supabase
            .from('authored_document_templates')
            .delete()
            .eq('id', id)
            .eq('tenant_id', tenantId); // Ensure tenant isolation

          if (error) throw error;
        } else {
          // Soft delete tenant template
          const { error } = await supabase
            .from('authored_document_templates')
            .update({ is_active: false })
            .eq('id', id)
            .eq('tenant_id', tenantId); // Ensure tenant isolation

          if (error) throw error;
        }
      }

      return true;
    } catch (error) {
      console.error('Error deleting template:', error);
      return false;
    }
  }
}

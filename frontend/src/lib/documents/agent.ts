// Document drafting agent integration

import { DocumentTemplate } from './templates';
import { DocumentDraftState, DocumentDraftMode } from './state';

// Define the prompt for document generation
export function createGenerationPrompt(
  caseContext: Record<string, unknown> = {},
  documentType: string,
  jurisdiction?: string
): string {
  return `
You are an expert legal document drafter with deep experience in personal injury law.
Generate a professional ${documentType} document for a personal injury case with the following details:

Case Context:
${formatCaseContext(caseContext)}

Requirements:
1. The document should be suitable for ${jurisdiction || 'standard U.S. jurisdiction'}.
2. Use professional, precise legal language appropriate for a ${documentType}.
3. Format the document with proper legal document structure and headings.
4. Include all standard sections typically found in a ${documentType}.
5. Base the document content on the provided case details.

Please respond with ONLY the complete document text, without any additional explanation or commentary.
`;
}

// Define the prompt for suggesting section improvements
export function createImprovementPrompt(
  sectionContent: string,
  sectionTitle: string,
  documentType: string,
  caseContext: Record<string, unknown> = {}
): string {
  return `
You are an expert legal document drafter with deep experience in personal injury law.
Review and suggest improvements for the following ${sectionTitle} section of a ${documentType}:

CURRENT CONTENT:
${sectionContent}

Case Context:
${formatCaseContext(caseContext)}

Provide 2-3 specific suggestions to improve this section. Focus on:
1. Legal precision and clarity
2. Strengthening arguments
3. Addressing potential weaknesses
4. Adding relevant details from the case context

Format each suggestion as a numbered list with a brief explanation.
`;
}

// Define the prompt for expanding a section based on user intent
export function createExpansionPrompt(
  sectionContent: string,
  sectionTitle: string,
  userIntent: string,
  caseContext: Record<string, unknown> = {}
): string {
  return `
You are an expert legal document drafter with deep experience in personal injury law.
Expand the following ${sectionTitle} section according to user request:

CURRENT CONTENT:
${sectionContent}

User request: "${userIntent}"

Case Context:
${formatCaseContext(caseContext)}

Provide an expanded version of this section that addresses the user's intent while maintaining professional legal tone and format.
`;
}

// Format case context for prompts
function formatCaseContext(caseContext: Record<string, unknown>): string {
  if (Object.keys(caseContext).length === 0) {
    return 'No specific case details provided.';
  }

  return Object.entries(caseContext)
    .map(([key, value]) => `- ${key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}: ${value}`)
    .join('\n');
}

// Generate document from scratch
export async function generateDocument(
  state: DocumentDraftState,
  caseContext: Record<string, unknown> = {}
): Promise<string> {
  // This would typically call an API or use OpenAI's SDK directly
  // For now, just return a placeholder
  const documentType = state.selectedTemplate?.category || 'legal document';
  const prompt = createGenerationPrompt(caseContext, documentType);

  console.log('Generating document with prompt:', prompt);

  // In production, this would call the LLM
  return `
[This is where the AI-generated document would appear]

DOCUMENT TYPE: ${documentType}

The document would be generated based on:
- Template: ${state.selectedTemplate?.name || 'No template selected'}
- Mode: ${state.mode}
- Variables: ${JSON.stringify(state.templateVariables)}
- Case Details: ${JSON.stringify(caseContext)}
`;
}

// Get improvement suggestions for a section
export async function getSectionSuggestions(
  sectionContent: string,
  sectionTitle: string,
  documentType: string,
  caseContext: Record<string, unknown> = {}
): Promise<string[]> {
  const prompt = createImprovementPrompt(sectionContent, sectionTitle, documentType, caseContext);

  console.log('Getting suggestions with prompt:', prompt);

  // In production, this would call the LLM
  return [
    "Add specific details about the extent of injuries to strengthen the damages claim.",
    "Include reference to relevant case law that supports your theory of negligence.",
    "Clarify the timeline of events to establish a clearer causation narrative."
  ];
}

// Expand a section based on user intent
export async function expandSection(
  sectionContent: string,
  sectionTitle: string,
  userIntent: string,
  caseContext: Record<string, unknown> = {}
): Promise<string> {
  const prompt = createExpansionPrompt(sectionContent, sectionTitle, userIntent, caseContext);

  console.log('Expanding section with prompt:', prompt);

  // In production, this would call the LLM
  return sectionContent + `\n\n[EXPANDED CONTENT BASED ON: "${userIntent}"]`;
}

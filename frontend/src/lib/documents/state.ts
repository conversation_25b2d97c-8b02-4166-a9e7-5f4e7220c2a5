// State management for document drafting

import { DocumentTemplate } from './templates';

export enum DocumentDraftMode {
  TEMPLATE = 'template',
  AI_ASSISTED = 'ai_assisted',
  HYBRID = 'hybrid'
}

export enum DocumentDraftStep {
  SELECT_TEMPLATE = 'select_template',
  COLLECT_VARIABLES = 'collect_variables',
  DRAFT_CONTENT = 'draft_content',
  REVIEW_DOCUMENT = 'review_document',
  FINALIZE_DOCUMENT = 'finalize_document'
}

export interface DocumentDraftState {
  // Document metadata
  id?: string;
  title: string;
  caseId?: string;
  clientId?: string;

  // Mode and step tracking
  mode: DocumentDraftMode;
  currentStep: DocumentDraftStep;

  // Template information
  selectedTemplateId?: string;
  selectedTemplate?: DocumentTemplate;
  templateVariables: Record<string, unknown>;

  // AI-generated content
  aiGeneratedContent?: string;
  aiSuggestions?: string[];

  // Document content
  sections: {
    id: string;
    title: string;
    content: string;
    isValid: boolean;
  }[];

  // Progress and validation
  missingVariables: string[];
  progressPercentage: number;
  validationErrors: string[];

  // UI state
  isDirty: boolean;
  isLoading: boolean;
  isSaving: boolean;
  lastSaved?: string;
}

export const initialDocumentDraftState: DocumentDraftState = {
  title: 'Untitled Document',
  mode: DocumentDraftMode.HYBRID,
  currentStep: DocumentDraftStep.SELECT_TEMPLATE,
  templateVariables: {},
  sections: [],
  missingVariables: [],
  progressPercentage: 0,
  validationErrors: [],
  isDirty: false,
  isLoading: false,
  isSaving: false
};

// Create a new document draft state with optional initial values
export function createDocumentDraftState(
  initialValues?: Partial<DocumentDraftState>
): DocumentDraftState {
  return {
    ...initialDocumentDraftState,
    ...initialValues
  };
}

// Update document draft state
export function updateDocumentDraftState(
  state: DocumentDraftState,
  updates: Partial<DocumentDraftState>
): DocumentDraftState {
  return {
    ...state,
    ...updates,
    isDirty: true
  };
}

// Calculate progress percentage based on current state
export function calculateProgress(state: DocumentDraftState): number {
  if (!state.selectedTemplateId) {
    return 0;
  }

  if (!state.selectedTemplate) {
    return 10;
  }

  if (state.missingVariables.length > 0) {
    // Calculate percentage based on filled variables
    const requiredVars = state.selectedTemplate.variables.filter(v => v.required).length;
    const filledVars = requiredVars - state.missingVariables.length;
    return 10 + Math.round((filledVars / requiredVars) * 40);
  }

  if (state.sections.length === 0) {
    return 50;
  }

  if (state.sections.some(s => !s.isValid)) {
    return 75;
  }

  return 100;
}

// Validate the document draft state
export function validateDocumentDraft(state: DocumentDraftState): string[] {
  const errors: string[] = [];

  if (!state.title) {
    errors.push('Document title is required');
  }

  if (state.mode !== DocumentDraftMode.AI_ASSISTED && !state.selectedTemplateId) {
    errors.push('Template selection is required');
  }

  if (state.mode !== DocumentDraftMode.AI_ASSISTED && state.missingVariables.length > 0) {
    errors.push(`Missing required variables: ${state.missingVariables.join(', ')}`);
  }

  if (state.sections.length === 0) {
    errors.push('Document must have at least one section');
  }

  if (state.sections.some(s => !s.content.trim())) {
    errors.push('All sections must have content');
  }

  return errors;
}

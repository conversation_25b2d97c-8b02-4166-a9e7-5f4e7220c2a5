// Document template types and utilities

export interface TemplateVariable {
  name: string;
  description: string;
  type: 'string' | 'date' | 'number' | 'boolean';
  required: boolean;
  default?: string;
}

export interface DocumentSection {
  id: string;
  title: string;
  content: string; // Contains variables in {{variable_name}} format
  required: boolean;
  order: number;
}

export interface DocumentTemplate {
  id: string;
  name: string;
  description: string;
  category: 'complaint' | 'motion' | 'letter' | 'agreement' | 'form' | 'authorization' | 'discovery' | 'other';
  documentType: 'case-specific' | 'general-operations';
  jurisdiction?: string;
  variables: TemplateVariable[];
  sections: DocumentSection[];
  createdAt: string;
  updatedAt: string;
}

// Document templates organized by category
export const SAMPLE_TEMPLATES: DocumentTemplate[] = [
  // CLIENT INTAKE & ENGAGEMENT DOCUMENTS
  {
    id: 'client-intake-form',
    name: 'Client Intake Form',
    description: 'Comprehensive client intake form for personal injury cases',
    category: 'form',
    documentType: 'case-specific',
    variables: [
      { name: 'client_name', description: 'Full name of the client', type: 'string', required: true },
      { name: 'client_address', description: 'Client\'s full address', type: 'string', required: true },
      { name: 'client_phone', description: 'Client\'s phone number', type: 'string', required: true },
      { name: 'client_email', description: 'Client\'s email address', type: 'string', required: true },
      { name: 'incident_date', description: 'Date of the incident', type: 'date', required: true },
      { name: 'incident_location', description: 'Location of the incident', type: 'string', required: true },
      { name: 'incident_description', description: 'Description of how the incident occurred', type: 'string', required: true },
      { name: 'injury_description', description: 'Description of injuries sustained', type: 'string', required: true },
      { name: 'medical_providers', description: 'List of medical providers seen', type: 'string', required: true },
    ],
    sections: [
      {
        id: 'header',
        title: 'Form Header',
        content: `PERSONAL INJURY CLIENT INTAKE FORM

CONFIDENTIAL

Date: [CURRENT DATE]`,
        required: true,
        order: 0
      },
      {
        id: 'personal-info',
        title: 'Personal Information',
        content: `PERSONAL INFORMATION

Full Name: \{\{client_name\}\}
Address: \{\{client_address\}\}
Phone Number: \{\{client_phone\}\}
Email Address: \{\{client_email\}\}
Date of Birth: [DOB]
Social Security Number: [SSN - LAST 4 DIGITS ONLY]
Marital Status: [MARITAL STATUS]
Spouse's Name (if applicable): [SPOUSE NAME]`,
        required: true,
        order: 1
      },
      {
        id: 'incident-info',
        title: 'Incident Information',
        content: `INCIDENT INFORMATION

Date of Incident: \{\{incident_date\}\}
Time of Incident: [TIME]
Location of Incident: \{\{incident_location\}\}

Description of how the incident occurred:
\{\{incident_description\}\}

Were there any witnesses? [YES/NO]
If yes, witness information: [WITNESS INFO]

Was a police report filed? [YES/NO]
If yes, police report number: [REPORT NUMBER]`,
        required: true,
        order: 2
      },
      {
        id: 'injury-info',
        title: 'Injury Information',
        content: `INJURY INFORMATION

Description of injuries sustained:
\{\{injury_description\}\}

Medical providers seen (including hospitals, doctors, therapists):
\{\{medical_providers\}\}

Were you transported by ambulance? [YES/NO]
Have you been hospitalized? [YES/NO]
If yes, dates of hospitalization: [DATES]

Prior injuries or pre-existing conditions: [PRIOR INJURIES]`,
        required: true,
        order: 3
      },
      {
        id: 'insurance-info',
        title: 'Insurance Information',
        content: `INSURANCE INFORMATION

Your insurance company: [YOUR INSURANCE]
Policy number: [YOUR POLICY NUMBER]
Do you have health insurance? [YES/NO]
Health insurance provider: [HEALTH INSURANCE]
Health insurance policy number: [HEALTH POLICY NUMBER]

Other party's insurance information (if known):
Company: [OTHER PARTY INSURANCE]
Policy number: [OTHER POLICY NUMBER]
Claim number (if assigned): [CLAIM NUMBER]`,
        required: true,
        order: 4
      },
      {
        id: 'employment-info',
        title: 'Employment Information',
        content: `EMPLOYMENT INFORMATION

Current employer: [EMPLOYER]
Job title: [JOB TITLE]
Have you missed work due to your injuries? [YES/NO]
If yes, dates missed: [DATES MISSED]
Estimated lost wages to date: [LOST WAGES]`,
        required: true,
        order: 5
      },
      {
        id: 'additional-info',
        title: 'Additional Information',
        content: `ADDITIONAL INFORMATION

How did you hear about our firm? [REFERRAL SOURCE]

Is there any additional information you believe would be helpful to your case?
[ADDITIONAL INFO]`,
        required: true,
        order: 6
      },
      {
        id: 'acknowledgment',
        title: 'Acknowledgment',
        content: `ACKNOWLEDGMENT

I, \{\{client_name\}\}, verify that the information provided above is true and accurate to the best of my knowledge.

____________________________
Signature

____________________________
Date`,
        required: true,
        order: 7
      }
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'letter-of-engagement',
    name: 'Letter of Engagement',
    description: 'Standard engagement letter for personal injury cases',
    category: 'letter',
    documentType: 'case-specific',
    variables: [
      { name: 'current_date', description: 'Current date', type: 'date', required: true },
      { name: 'client_name', description: 'Full name of the client', type: 'string', required: true },
      { name: 'client_address', description: 'Client\'s full address', type: 'string', required: true },
      { name: 'attorney_name', description: 'Attorney\'s full name', type: 'string', required: true },
      { name: 'firm_name', description: 'Law firm name', type: 'string', required: true },
      { name: 'matter_description', description: 'Brief description of the legal matter', type: 'string', required: true },
      { name: 'incident_date', description: 'Date of the incident', type: 'date', required: true },
      { name: 'fee_percentage', description: 'Contingency fee percentage', type: 'string', required: true },
    ],
    sections: [
      {
        id: 'letterhead',
        title: 'Letterhead',
        content: `\{\{firm_name\}\}
[FIRM ADDRESS]
[FIRM PHONE]
[FIRM EMAIL]
[FIRM WEBSITE]

\{\{current_date\}\}`,
        required: true,
        order: 0
      },
      {
        id: 'client-address',
        title: 'Client Address',
        content: `\{\{client_name\}\}
\{\{client_address\}\}`,
        required: true,
        order: 1
      },
      {
        id: 'subject',
        title: 'Subject Line',
        content: `Re: Engagement for Legal Representation - \{\{matter_description\}\}`,
        required: true,
        order: 2
      },
      {
        id: 'greeting',
        title: 'Greeting',
        content: `Dear \{\{client_name\}\}:`,
        required: true,
        order: 3
      },
      {
        id: 'introduction',
        title: 'Introduction',
        content: `Thank you for selecting our firm to represent you in connection with your personal injury matter arising from the incident that occurred on \{\{incident_date\}\}. This letter confirms our discussion about the legal services we will provide and the terms of our engagement.`,
        required: true,
        order: 4
      },
      {
        id: 'scope',
        title: 'Scope of Representation',
        content: `SCOPE OF REPRESENTATION

Our firm will represent you in connection with your claims for damages arising from the incident that occurred on \{\{incident_date\}\}. Our representation includes investigating your claim, negotiating with insurance companies, and, if necessary, filing and prosecuting a lawsuit on your behalf. Our goal is to obtain the maximum recovery possible for your injuries and damages.

Our representation does not include handling any other legal matters that may arise during the course of our representation, unless we specifically agree to do so in writing.`,
        required: true,
        order: 5
      },
      {
        id: 'fees',
        title: 'Fees and Expenses',
        content: `FEES AND EXPENSES

As we discussed, we will represent you on a contingency fee basis. This means that our fee will be a percentage of any recovery we obtain on your behalf, whether through settlement, judgment, or otherwise. Our fee will be \{\{fee_percentage\}\} of the gross recovery before the deduction of costs and expenses.

In addition to our fee, you will be responsible for the costs and expenses incurred in pursuing your claim. These costs may include, but are not limited to, filing fees, deposition costs, expert witness fees, medical record retrieval costs, and investigation expenses. These costs and expenses will be deducted from your portion of the recovery after our fee is deducted.

If we do not recover any money on your behalf, you will not owe us any fees. However, you would still be responsible for the costs and expenses incurred, as discussed above.`,
        required: true,
        order: 6
      },
      {
        id: 'client-responsibilities',
        title: 'Client Responsibilities',
        content: `CLIENT RESPONSIBILITIES

To effectively represent you, we need your full cooperation. Your responsibilities include:

1. Providing us with complete and accurate information about your case
2. Promptly informing us of any changes in your contact information
3. Keeping us informed of any developments related to your case
4. Assisting us in responding to discovery requests
5. Attending meetings, depositions, and court appearances as required
6. Following your healthcare providers' recommendations for treatment
7. Refraining from discussing your case with others, especially on social media
8. Not communicating directly with the opposing party or their insurance company without our knowledge`,
        required: true,
        order: 7
      },
      {
        id: 'communication',
        title: 'Communication',
        content: `COMMUNICATION

We will keep you informed of significant developments in your case and consult with you before making major decisions. We encourage you to contact us with any questions or concerns. We will make every effort to respond to your inquiries promptly.`,
        required: true,
        order: 8
      },
      {
        id: 'termination',
        title: 'Termination',
        content: `TERMINATION

You have the right to terminate our representation at any time. If you do so, you will be responsible for payment of costs and expenses incurred up to the date of termination.

We also reserve the right to withdraw from representation for good cause, such as your failure to cooperate with us, your insistence that we engage in unprofessional conduct, or non-payment of costs and expenses. We will provide you with written notice of any such withdrawal.`,
        required: true,
        order: 9
      },
      {
        id: 'file-retention',
        title: 'File Retention',
        content: `FILE RETENTION

We will maintain your file for [TIME PERIOD] after the conclusion of your case. After that time, we may destroy the file without further notice to you. If you wish to obtain your file at the conclusion of your case, please let us know.`,
        required: true,
        order: 10
      },
      {
        id: 'closing',
        title: 'Closing',
        content: `We appreciate your confidence in us and look forward to representing you in this matter. If you have any questions about this letter or our representation, please do not hesitate to contact me.

If the terms of our engagement as set forth in this letter are acceptable, please sign and date the enclosed copy of this letter and return it to our office.

Sincerely,


\{\{attorney_name\}\}
\{\{firm_name\}\}`,
        required: true,
        order: 11
      },
      {
        id: 'acceptance',
        title: 'Acceptance',
        content: `ACCEPTANCE

I have read and understand the terms of engagement set forth above, and I agree to them.


____________________________     ____________________
\{\{client_name\}\}                   Date`,
        required: true,
        order: 12
      }
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'contingency-fee-agreement',
    name: 'Contingency Fee Agreement',
    description: 'Standard contingency fee agreement for personal injury cases',
    category: 'agreement',
    documentType: 'case-specific',
    variables: [
      { name: 'current_date', description: 'Current date', type: 'date', required: true },
      { name: 'client_name', description: 'Full name of the client', type: 'string', required: true },
      { name: 'attorney_name', description: 'Attorney\'s full name', type: 'string', required: true },
      { name: 'firm_name', description: 'Law firm name', type: 'string', required: true },
      { name: 'matter_description', description: 'Brief description of the legal matter', type: 'string', required: true },
      { name: 'incident_date', description: 'Date of the incident', type: 'date', required: true },
      { name: 'standard_fee_percentage', description: 'Standard contingency fee percentage', type: 'string', required: true },
      { name: 'trial_fee_percentage', description: 'Fee percentage if case goes to trial', type: 'string', required: true },
      { name: 'appeal_fee_percentage', description: 'Fee percentage if case is appealed', type: 'string', required: true }
    ],
    sections: [
      {
        id: 'header',
        title: 'Header',
        content: `CONTINGENCY FEE AGREEMENT

Date: \{\{current_date\}\}`,
        required: true,
        order: 0
      },
      {
        id: 'parties',
        title: 'Parties',
        content: `THIS AGREEMENT is made between \{\{client_name\}\} (hereinafter referred to as "Client") and \{\{firm_name\}\}, including its attorneys, staff, and agents (hereinafter referred to as "Attorney").`,
        required: true,
        order: 1
      },
      {
        id: 'scope',
        title: 'Scope of Representation',
        content: `1. SCOPE OF REPRESENTATION

Client retains Attorney to represent Client in connection with claims for damages arising from \{\{matter_description\}\} that occurred on or about \{\{incident_date\}\}, as well as any related matters that may arise directly from this incident (the "Matter").

Attorney will investigate the claim, negotiate with relevant parties, and if necessary, file and litigate a lawsuit. Attorney will represent Client until a settlement is reached, a final judgment is entered, or until Attorney withdraws or is discharged as provided in this Agreement.`,
        required: true,
        order: 2
      },
      {
        id: 'fee-structure',
        title: 'Fee Structure',
        content: `2. CONTINGENCY FEE ARRANGEMENT

a. Attorney's Fee: Attorney will receive as compensation for legal services a percentage of the gross recovery (before deduction of costs) as follows:

   - \{\{standard_fee_percentage\}\}% if the matter is resolved prior to filing a lawsuit
   - \{\{trial_fee_percentage\}\}% if a lawsuit is filed
   - \{\{appeal_fee_percentage\}\}% if an appeal is filed by any party

b. Gross Recovery: "Gross recovery" means the total amount of money received by settlement, judgment, or otherwise, including any attorney's fees awarded.

c. No Recovery, No Fee: If there is no recovery, Client will not owe Attorney any fees for legal services. However, Client remains responsible for costs and expenses as detailed below.`,
        required: true,
        order: 3
      },
      {
        id: 'costs',
        title: 'Costs and Expenses',
        content: `3. COSTS AND EXPENSES

a. Client is responsible for paying all costs and expenses incurred in handling Client's matter, regardless of whether there is a recovery.

b. Costs and expenses may include, but are not limited to: court filing fees, deposition costs, expert witness fees, investigation expenses, medical report fees, postage for large mailings, photocopying charges, travel expenses, and other similar expenses.

c. Attorney may advance costs and expenses on Client's behalf. If so, Client agrees that such advanced costs and expenses will be deducted from Client's portion of the recovery after the contingency fee is calculated.

d. If there is no recovery, Client agrees to reimburse Attorney for all costs and expenses advanced within 30 days of receiving a final statement.`,
        required: true,
        order: 4
      },
      {
        id: 'settlement',
        title: 'Settlement Authority',
        content: `4. SETTLEMENT AUTHORITY

a. Attorney will not settle Client's case without Client's consent.

b. Client agrees not to make settlement negotiations with any person or entity without Attorney's knowledge and presence.`,
        required: true,
        order: 5
      },
      {
        id: 'liens',
        title: 'Liens and Third-Party Claims',
        content: `5. LIENS AND THIRD-PARTY CLAIMS

a. Client understands that if any person or entity claims an interest in the recovery obtained in this matter (including but not limited to healthcare providers, insurance companies, or government agencies), these claims must be resolved out of the recovery.

b. Client authorizes Attorney to pay directly from Client's portion of the recovery any and all medical bills, liens, subrogation claims, or other claims that Client is legally obligated to pay from the proceeds of this case.

c. Client acknowledges that Attorney may be legally required to pay third parties out of the settlement funds.`,
        required: true,
        order: 6
      },
      {
        id: 'client-responsibilities',
        title: 'Client Responsibilities',
        content: `6. CLIENT RESPONSIBILITIES

Client agrees to:

a. Provide Attorney with complete and accurate facts and information regarding the Matter
b. Cooperate fully with Attorney at all times
c. Keep Attorney informed of any changes in address, telephone number, or circumstances
d. Appear at all legal proceedings when Attorney determines Client's appearance is necessary
e. Avoid discussing the case with others, particularly on social media platforms
f. Provide Attorney with copies of all documents in Client's possession that relate to the Matter
g. Assist Attorney in responding to discovery requests
h. Sign all necessary documents promptly`,
        required: true,
        order: 7
      },
      {
        id: 'termination',
        title: 'Termination of Representation',
        content: `7. TERMINATION OF REPRESENTATION

a. Client may terminate this Agreement at any time by written notice to Attorney. If Client terminates this Agreement, Client agrees to pay Attorney for all costs and expenses incurred by Attorney up to the date of termination.

b. If Client terminates this Agreement and later recovers money through settlement or judgment on this matter, Client agrees to pay Attorney:
   (i) All costs and expenses advanced or incurred by Attorney; and
   (ii) The reasonable value of Attorney's services up to the date of termination, which will not exceed the contingent fee amount that would have been due under this Agreement.

c. Attorney may withdraw from representation for good cause, including but not limited to:
   (i) Client's failure to cooperate with Attorney
   (ii) Client's insistence on pursuing an objective that Attorney considers imprudent, unethical, or illegal
   (iii) Client's misrepresentation of or failure to disclose material facts
   (iv) Any other reason permitted under the applicable Rules of Professional Conduct`,
        required: true,
        order: 8
      },
      {
        id: 'disclaimer',
        title: 'Disclaimer of Guarantee',
        content: `8. DISCLAIMER OF GUARANTEE

a. Attorney cannot and does not guarantee the outcome of Client's case.

b. All expressions relative to the possible outcome are Attorney's opinions based on Attorney's experience and are not guarantees of any particular result.`,
        required: true,
        order: 9
      },
      {
        id: 'file-retention',
        title: 'File Retention',
        content: `9. FILE RETENTION

Attorney will maintain Client's file for a period of [TIME PERIOD] after this matter concludes. After that time, Attorney may destroy the file without further notice to Client. If Client wishes to obtain the file, Client must notify Attorney in writing before the end of this time period.`,
        required: true,
        order: 10
      },
      {
        id: 'signatures',
        title: 'Signatures',
        content: `CLIENT ACKNOWLEDGES THAT CLIENT HAS READ THIS AGREEMENT IN ITS ENTIRETY, THAT CLIENT UNDERSTANDS IT, AND THAT CLIENT AGREES TO ITS TERMS AND CONDITIONS.


______________________________     __________________
\{\{client_name\}\}                     Date


______________________________     __________________
\{\{attorney_name\}\}                   Date
On behalf of \{\{firm_name\}\}`,
        required: true,
        order: 11
      }
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'hipaa-authorization',
    name: 'HIPAA Authorization Form',
    description: 'Authorization for release of medical information under HIPAA',
    category: 'authorization',
    documentType: 'case-specific',
    variables: [
      { name: 'client_name', description: 'Full name of the client', type: 'string', required: true },
      { name: 'client_dob', description: 'Client\'s date of birth', type: 'date', required: true },
      { name: 'client_address', description: 'Client\'s full address', type: 'string', required: true },
      { name: 'client_phone', description: 'Client\'s phone number', type: 'string', required: true },
      { name: 'attorney_name', description: 'Attorney\'s full name', type: 'string', required: true },
      { name: 'firm_name', description: 'Law firm name', type: 'string', required: true },
      { name: 'firm_address', description: 'Law firm address', type: 'string', required: true },
      { name: 'incident_date', description: 'Date of the incident', type: 'date', required: true },
    ],
    sections: [
      {
        id: 'header',
        title: 'Header',
        content: `AUTHORIZATION FOR RELEASE OF
PROTECTED HEALTH INFORMATION
(Pursuant to HIPAA and Texas Medical Privacy Laws)`,
        required: true,
        order: 0
      },
      {
        id: 'patient-info',
        title: 'Patient Information',
        content: `PATIENT INFORMATION

Name: \{\{client_name\}\}
Date of Birth: \{\{client_dob\}\}
Address: \{\{client_address\}\}
Phone: \{\{client_phone\}\}`,
        required: true,
        order: 1
      },
      {
        id: 'authorization',
        title: 'Authorization',
        content: `I, \{\{client_name\}\}, authorize any healthcare provider, including but not limited to any physician, hospital, clinic, laboratory, pharmacy, or other healthcare professional or facility that has provided treatment, services, or supplies to me, to release my protected health information as detailed in this authorization to:

\{\{firm_name\}\}
\{\{attorney_name\}\}
\{\{firm_address\}\}
[FIRM PHONE NUMBER]
[FIRM FAX NUMBER]`,
        required: true,
        order: 2
      },
      {
        id: 'information',
        title: 'Information to be Released',
        content: `INFORMATION TO BE RELEASED

I authorize the release of my complete medical record, including:

• All medical reports, records, charts, notes, histories, laboratory and diagnostic test results
• All X-rays, MRIs, CT scans, and other imaging studies
• All hospital and emergency room records, including admission and discharge summaries
• All pharmacy and prescription records
• All billing and payment records
• All records related to drug or alcohol treatment
• All records related to mental health treatment
• All records related to HIV/AIDS diagnosis and treatment
• All records related to communicable diseases

This authorization specifically includes records from the following date ranges:
• All records from five (5) years prior to the date of injury/incident (\{\{incident_date\}\})
• All records from the date of injury/incident to the present
• All future records until this authorization expires`,
        required: true,
        order: 3
      },
      {
        id: 'purpose',
        title: 'Purpose of Disclosure',
        content: `PURPOSE OF DISCLOSURE

This information is being requested for legal purposes, including but not limited to investigation, evaluation, prosecution, and/or settlement of a legal claim arising from an incident that occurred on or about \{\{incident_date\}\}.`,
        required: true,
        order: 4
      },
      {
        id: 'expiration',
        title: 'Expiration',
        content: `EXPIRATION

This authorization will expire upon the conclusion of my legal matter or two (2) years from the date of my signature below, whichever occurs later.`,
        required: true,
        order: 5
      },
      {
        id: 'revocation',
        title: 'Revocation',
        content: `REVOCATION

I understand that I have the right to revoke this authorization at any time by sending written notification to the healthcare providers from whom I have requested that information be released. I understand that a revocation is not effective to the extent that the providers have relied on this authorization to release information prior to receiving my written notice of revocation.`,
        required: true,
        order: 6
      },
      {
        id: 'redisclosure',
        title: 'Redisclosure Notice',
        content: `NOTICE OF POTENTIAL FOR REDISCLOSURE

I understand that information disclosed pursuant to this authorization may be subject to redisclosure by the recipient and may no longer be protected by federal or state law. However, I understand that \{\{firm_name\}\} will take reasonable steps to maintain the confidentiality of my health information.`,
        required: true,
        order: 7
      },
      {
        id: 'rights',
        title: 'Patient Rights',
        content: `PATIENT RIGHTS

I understand that:
• I may refuse to sign this authorization and that my refusal will not affect my ability to obtain treatment, payment, or eligibility for benefits
• I may inspect or copy any information used/disclosed under this authorization
• I am entitled to receive a copy of this authorization after I sign it
• I may revoke this authorization at any time, subject to the limitations noted above`,
        required: true,
        order: 8
      },
      {
        id: 'delivery',
        title: 'Delivery Instructions',
        content: `DELIVERY INSTRUCTIONS

The authorized medical records are to be delivered as follows:

By Mail: \{\{firm_name\}\}, \{\{firm_address\}\}
By Fax: [FIRM FAX NUMBER]
By Email: [FIRM EMAIL ADDRESS]`,
        required: true,
        order: 9
      },
      {
        id: 'signature',
        title: 'Signature',
        content: `By signing below, I acknowledge that I have read and understand this authorization.


______________________________     __________________
Signature of Patient                 Date
or Legal Representative


______________________________
Printed Name


If signed by Legal Representative, relationship to Patient: _________________


______________________________     __________________
Witness Signature                    Date


______________________________
Witness Printed Name`,
        required: true,
        order: 10
      }
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  // PRE-LITIGATION DOCUMENTS
  {
    id: 'complaint-personal-injury',
    name: 'Personal Injury Complaint',
    description: 'Standard complaint for personal injury cases in Texas',
    category: 'complaint',
    documentType: 'case-specific',
    jurisdiction: 'Texas',
    variables: [
      { name: 'client_name', description: 'Full name of the client', type: 'string', required: true },
      { name: 'defendant_name', description: 'Name of the defendant', type: 'string', required: true },
      { name: 'incident_date', description: 'Date of the incident', type: 'date', required: true },
      { name: 'incident_location', description: 'Location where the incident occurred', type: 'string', required: true },
      { name: 'injury_description', description: 'Description of injuries sustained', type: 'string', required: true },
      { name: 'damages_amount', description: 'Amount of damages claimed', type: 'number', required: true },
      { name: 'court_name', description: 'Name of the court', type: 'string', required: true },
      { name: 'case_number', description: 'Case number if available', type: 'string', required: false },
    ],
    sections: [
      {
        id: 'header',
        title: 'Header',
        content: `IN THE \{\{court_name\}\}

\{\{client_name\}\},
Plaintiff,

v.

\{\{defendant_name\}\},
Defendant.

CAUSE NO. \{\{case_number\}\}`,
        required: true,
        order: 0
      },
      {
        id: 'introduction',
        title: 'Introduction',
        content: `PLAINTIFF'S ORIGINAL COMPLAINT

TO THE HONORABLE JUDGE OF SAID COURT:

COMES NOW, \{\{client_name\}\}, Plaintiff in the above-entitled and numbered cause, and files this Original Complaint against \{\{defendant_name\}\}, Defendant, and would respectfully show unto the Court as follows:`,
        required: true,
        order: 1
      },
      {
        id: 'parties',
        title: 'Parties',
        content: `PARTIES

1. Plaintiff \{\{client_name\}\} is an individual residing in Texas.

2. Defendant \{\{defendant_name\}\} is [INDIVIDUAL/ENTITY] and may be served with process at [ADDRESS].`,
        required: true,
        order: 2
      },
      {
        id: 'jurisdiction',
        title: 'Jurisdiction and Venue',
        content: `JURISDICTION AND VENUE

3. This Court has jurisdiction over this matter as the amount in controversy exceeds the minimum jurisdictional limits of this Court.

4. Venue is proper in this Court pursuant to Texas Civil Practice & Remedies Code § 15.002 because all or a substantial part of the events or omissions giving rise to the claim occurred in this county.`,
        required: true,
        order: 3
      },
      {
        id: 'facts',
        title: 'Facts',
        content: `FACTS

5. On or about \{\{incident_date\}\}, Plaintiff was at \{\{incident_location\}\}.

6. [DESCRIPTION OF INCIDENT]

7. As a result of the incident, Plaintiff suffered \{\{injury_description\}\}.

8. The incident was proximately caused by the negligence of the Defendant.`,
        required: true,
        order: 4
      },
      {
        id: 'negligence',
        title: 'Negligence',
        content: `NEGLIGENCE

9. Defendant owed Plaintiff a duty of reasonable care.

10. Defendant breached that duty by:
   a. [SPECIFIC BREACH]
   b. [SPECIFIC BREACH]
   c. [SPECIFIC BREACH]

11. As a proximate result of Defendant's breach, Plaintiff suffered injuries and damages.`,
        required: true,
        order: 5
      },
      {
        id: 'damages',
        title: 'Damages',
        content: `DAMAGES

12. As a direct and proximate result of Defendant's negligence, Plaintiff has suffered damages in excess of \{\{damages_amount\}\}, including:

   a. Past and future medical expenses;
   b. Past and future physical pain and suffering;
   c. Past and future mental anguish;
   d. Past and future physical impairment;
   e. Lost wages and loss of earning capacity; and
   f. Property damage.`,
        required: true,
        order: 6
      },
      {
        id: 'prayer',
        title: 'Prayer',
        content: `PRAYER

WHEREFORE, PREMISES CONSIDERED, Plaintiff prays that Defendant be cited to appear and answer herein, and that upon final trial, Plaintiff have and recover:

1. Judgment against Defendant for a sum in excess of \{\{damages_amount\}\}, which is within the jurisdictional limits of this Court;
2. Pre-judgment and post-judgment interest as allowed by law;
3. Costs of Court; and
4. Such other and further relief to which Plaintiff may be justly entitled.

Respectfully submitted,

[ATTORNEY SIGNATURE BLOCK]

ATTORNEY FOR PLAINTIFF`,
        required: true,
        order: 7
      }
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'demand-letter',
    name: 'Demand Letter',
    description: 'Standard demand letter for personal injury cases',
    category: 'letter',
    documentType: 'case-specific',
    variables: [
      { name: 'attorney_name', description: 'Name of the attorney', type: 'string', required: true },
      { name: 'client_name', description: 'Full name of the client', type: 'string', required: true },
      { name: 'recipient_name', description: 'Name of the recipient', type: 'string', required: true },
      { name: 'recipient_address', description: 'Address of the recipient', type: 'string', required: true },
      { name: 'incident_date', description: 'Date of the incident', type: 'date', required: true },
      { name: 'incident_description', description: 'Description of the incident', type: 'string', required: true },
      { name: 'injury_description', description: 'Description of injuries sustained', type: 'string', required: true },
      { name: 'medical_expenses', description: 'Total medical expenses', type: 'number', required: true },
      { name: 'lost_wages', description: 'Total lost wages', type: 'number', required: true },
      { name: 'pain_suffering', description: 'Amount for pain and suffering', type: 'number', required: true },
      { name: 'demand_amount', description: 'Total amount demanded', type: 'number', required: true },
      { name: 'response_deadline', description: 'Deadline for response', type: 'date', required: true },
    ],
    sections: [
      {
        id: 'letterhead',
        title: 'Letterhead',
        content: `[LAW FIRM LETTERHEAD]

[DATE]

VIA CERTIFIED MAIL, RETURN RECEIPT REQUESTED

\{\{recipient_name\}\}
\{\{recipient_address\}\}`,
        required: true,
        order: 0
      },
      {
        id: 'subject',
        title: 'Subject Line',
        content: `RE: Personal Injury Claim of \{\{client_name\}\}
    Date of Incident: \{\{incident_date\}\}`,
        required: true,
        order: 1
      },
      {
        id: 'greeting',
        title: 'Greeting',
        content: `Dear \{\{recipient_name\}\}:`,
        required: true,
        order: 2
      },
      {
        id: 'introduction',
        title: 'Introduction',
        content: `Please be advised that this office represents \{\{client_name\}\} with respect to injuries and damages sustained in an incident that occurred on \{\{incident_date\}\}. The purpose of this letter is to make a formal demand for compensation for the injuries and damages suffered by my client as a result of this incident.`,
        required: true,
        order: 3
      },
      {
        id: 'incident',
        title: 'Incident Description',
        content: `INCIDENT

\{\{incident_description\}\}`,
        required: true,
        order: 4
      },
      {
        id: 'injuries',
        title: 'Injuries and Treatment',
        content: `INJURIES AND MEDICAL TREATMENT

As a result of this incident, my client suffered \{\{injury_description\}\}.

[DETAILED DESCRIPTION OF MEDICAL TREATMENT]`,
        required: true,
        order: 5
      },
      {
        id: 'damages',
        title: 'Damages',
        content: `DAMAGES

My client has incurred the following damages as a result of this incident:

1. Medical Expenses: \$\{\{medical_expenses\}\}
2. Lost Wages: \$\{\{lost_wages\}\}
3. Pain and Suffering: \$\{\{pain_suffering\}\}

TOTAL: \$\{\{demand_amount\}\}`,
        required: true,
        order: 6
      },
      {
        id: 'demand',
        title: 'Demand',
        content: `DEMAND

Based on the foregoing, my client hereby demands payment in the amount of $\{\{demand_amount\}\} to settle this claim. This offer will remain open for \{\{response_deadline\}\} days from the date of this letter, after which we will file suit against you without further notice.`,
        required: true,
        order: 7
      },
      {
        id: 'closing',
        title: 'Closing',
        content: `If you have any questions, please do not hesitate to contact my office.

Sincerely,

\{\{attorney_name\}\}
Attorney at Law`,
        required: true,
        order: 8
      }
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// Helper function to get a template by ID
export function getTemplateById(id: string): DocumentTemplate | undefined {
  return SAMPLE_TEMPLATES.find(template => template.id === id);
}

// Helper function to combine template sections into a single document
export function compileTemplate(template: DocumentTemplate, variables: Record<string, unknown>): string {
  return template.sections
    .sort((a, b) => a.order - b.order)
    .map(section => {
      let content = section.content;

      // Replace all variables in the content
      Object.entries(variables).forEach(([key, value]) => {
        const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
        content = content.replace(regex, value?.toString() || '');
      });

      return content;
    })
    .join('\n\n');
}

// Function to identify missing required variables
export function getMissingVariables(template: DocumentTemplate, variables: Record<string, unknown>): string[] {
  return template.variables
    .filter(v => v.required && !variables[v.name])
    .map(v => v.name);
}

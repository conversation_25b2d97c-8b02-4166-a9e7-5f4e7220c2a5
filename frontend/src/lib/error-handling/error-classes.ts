/**
 * AG-UI Error Classes
 * Concrete implementations of error types for use in the application
 */

import { AGError as IAGError, ErrorSeverity, ErrorType } from './error-types';

/**
 * Base AGError implementation that extends Error
 */
export class AGError extends Error implements IAGError {
  type: ErrorType;
  severity: ErrorSeverity;
  retryable: boolean;
  originalError?: Error | unknown;
  statusCode?: number;
  context?: Record<string, unknown>;
  timestamp: number;
  code?: string;
  metadata?: Record<string, unknown>;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    severity: ErrorSeverity = ErrorSeverity.ERROR,
    retryable = true
  ) {
    super(message);
    this.name = 'AGError';
    this.type = type;
    this.severity = severity;
    this.retryable = retryable;
    this.timestamp = Date.now();

    // Ensure the prototype chain is properly maintained
    Object.setPrototypeOf(this, AGError.prototype);
  }
}

/**
 * Network error implementation
 */
export class NetworkError extends AGError {
  constructor(message: string) {
    super(message, ErrorType.NETWORK_OFFLINE, ErrorSeverity.WARNING, true);
    this.name = 'NetworkError';
    this.code = 'NETWORK_ERROR';
  }
}

/**
 * API error implementation
 */
export class APIError extends AGError {
  constructor(message: string, statusCode?: number) {
    super(
      message,
      statusCode ? mapStatusToErrorType(statusCode) : ErrorType.SERVER_ERROR,
      ErrorSeverity.ERROR,
      isRetryableStatus(statusCode)
    );
    this.name = 'APIError';
    this.statusCode = statusCode;
    this.code = 'API_ERROR';
  }
}

/**
 * Map HTTP status code to error type
 */
function mapStatusToErrorType(status: number): ErrorType {
  if (status >= 500) return ErrorType.SERVER_ERROR;
  if (status === 429) return ErrorType.QUOTA_EXCEEDED;
  if (status === 408) return ErrorType.NETWORK_TIMEOUT;
  if (status === 404) return ErrorType.SERVER_ERROR;
  if (status === 403) return ErrorType.ACCESS_DENIED;
  if (status === 401) return ErrorType.AUTH_EXPIRED;
  if (status === 400) return ErrorType.DATA_VALIDATION;
  
  return ErrorType.UNKNOWN;
}

/**
 * Determine if a status code should trigger a retry
 */
function isRetryableStatus(status?: number): boolean {
  if (!status) return false;
  // 5xx errors and some specific others are retryable
  return status >= 500 || status === 429 || status === 408;
}

// Export a simple enum for error codes to replace AGErrorCodes
export enum AGErrorCodes {
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED'
}

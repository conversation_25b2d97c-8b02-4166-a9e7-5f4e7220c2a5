import { <PERSON>t<PERSON><PERSON>A<PERSON> } from "@langchain/openai";
// Temporarily comment out until package is installed properly
// import { GoogleGenerativeAI } from "@langchain/google-genai";
// Create a temporary placeholder for GoogleGenerativeAI
class GoogleGenerativeAI {
  constructor(options: any) {
    console.warn("GoogleGenerativeAI is a placeholder. Install @langchain/google-genai");
  }
  // Add minimal methods to match ChatOpenAI interface
  async invoke(messages: any): Promise<any> {
    throw new Error("GoogleGenerativeAI is not implemented");
  }
}

import { SystemMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import { StateManager } from "../core/state";
import { InsightGraphState, InsightSource } from "../types/insight-graph";

// Define EntityType enum here to avoid type/value confusion
enum EntityType {
  Case = "case",
  Document = "document",
  Deadline = "deadline",
  Activity = "activity"
}

// Extended Activity interface to match the expected properties in our code
interface Activity {
  id?: string;
  type?: string;
  timestamp?: string;
  userId?: string;
  caseId?: string;
  documentId?: string;
  description?: string;
  metadata?: Record<string, unknown>;
  [key: string]: any; // Allow for other properties
}

// Re-export the types from the application
import { Insight } from "../../types";
import { StructuredOutputParser } from "langchain/output_parsers";
import { z } from "zod";

/**
 * Agent types in the swarm, each specializing in different aspects of insight generation
 */
export type SwarmAgentType =
  | "ActivityAnalyzer"
  | "CaseSpecialist"
  | "DocumentAnalyzer"
  | "TimelineManager"
  | "ActionRecommender";

/**
 * A single agent in the swarm
 */
export interface SwarmAgentConfig {
  id: string;
  type: SwarmAgentType;
  expertise: string[];
  systemPrompt: string;
  outputSchema: z.ZodType<any>;
}

/**
 * Swarm Agent System for collaborative insight generation
 * - Employs multiple specialized agents
 * - Enables agent communication and collaboration
 * - Aggregates results for comprehensive insights
 */
export class SwarmAgentSystem {
  private llm: ChatOpenAI | GoogleGenerativeAI;
  private agents: Map<string, SwarmAgentConfig>;
  private provider: "openai" | "google";
  private modelName: string;
  private temperature: number;
  // New tracking variables for collaboration
  private collaborationEnabled: boolean;
  private maxCollaborationRequests: number;
  private collaborationMemory: Map<string, Array<{question: string, response: string}>>;

  constructor(
    options: {
      provider?: "openai" | "google";
      modelName?: string;
      temperature?: number;
      enableCollaboration?: boolean;
      maxCollaborationRequests?: number;
    } = {}
  ) {
    // Initialize LLM configuration
    this.provider = options.provider || "openai";
    this.modelName = options.modelName || (this.provider === "openai" ? "gpt-4-turbo" : "gemini-pro");
    this.temperature = options.temperature || 0.2;

    // Initialize collaboration settings
    this.collaborationEnabled = options.enableCollaboration !== false;
    this.maxCollaborationRequests = options.maxCollaborationRequests || 3;
    this.collaborationMemory = new Map();

    if (this.provider === "openai") {
      this.llm = new ChatOpenAI({
        modelName: this.modelName,
        temperature: this.temperature,
        apiKey: process.env.OPENAI_API_KEY,
      });
    } else {
      this.llm = new GoogleGenerativeAI({
        modelName: this.modelName,
        temperature: this.temperature,
        apiKey: process.env.GOOGLE_API_KEY as string,
      });
    }

    // Initialize agent configurations
    this.agents = new Map();
    this.initializeAgents();
  }

  /**
   * Initialize the standard set of agents in the swarm
   */
  private initializeAgents() {
    // Activity Analyzer Agent
    this.agents.set("ActivityAnalyzer", {
      id: "activity_analyzer",
      type: "ActivityAnalyzer",
      expertise: ["user behavior", "activity patterns", "trend identification"],
      systemPrompt: `You are an Activity Analyzer specializing in identifying patterns and insights from user activities.
Your task is to analyze activity logs and identify:
1. Important trends in user behavior
2. Anomalies or changes in patterns
3. High-value activities that deserve attention
4. Groups of related activities that tell a story

Focus on being specific, actionable, and providing context for why these patterns matter.`,
      outputSchema: z.object({
        patterns: z.array(z.string()),
        anomalies: z.array(z.string()),
        highValueActivities: z.array(z.string()),
        groupedInsights: z.array(z.object({
          theme: z.string(),
          activities: z.array(z.string()),
          insight: z.string(),
          priority: z.number(),
        })),
      }),
    });

    // Case Specialist Agent
    this.agents.set("CaseSpecialist", {
      id: "case_specialist",
      type: "CaseSpecialist",
      expertise: ["legal case analysis", "case progression", "risk assessment"],
      systemPrompt: `You are a Case Specialist focusing on insights related to legal cases.
Your task is to analyze case information and activities to identify:
1. Critical case developments
2. Potential risks or issues
3. Next steps and recommended actions
4. Deadline sensitivity and timeline management

Provide legal context where appropriate and focus on actionable insights.`,
      outputSchema: z.object({
        caseDevelopments: z.array(z.string()),
        riskAssessment: z.array(z.object({
          risk: z.string(),
          severity: z.number(),
          mitigation: z.string(),
        })),
        recommendedActions: z.array(z.string()),
        timelineConcerns: z.array(z.string()),
      }),
    });

    // Document Analyzer Agent
    this.agents.set("DocumentAnalyzer", {
      id: "document_analyzer",
      type: "DocumentAnalyzer",
      expertise: ["document analysis", "content extraction", "document relationships"],
      systemPrompt: `You are a Document Analyzer specializing in extracting insights from legal documents.
Your task is to analyze document content and metadata to identify:
1. Key information and critical content
2. Document relationships and dependencies
3. Required document actions (review, update, file)
4. Content gaps or inconsistencies

Focus on document-specific insights that help the legal team manage their document workflow.`,
      outputSchema: z.object({
        keyInformation: z.array(z.string()),
        documentRelationships: z.array(z.object({
          primaryDoc: z.string(),
          relatedDocs: z.array(z.string()),
          relationship: z.string(),
        })),
        requiredActions: z.array(z.object({
          documentId: z.string(),
          action: z.string(),
          priority: z.number(),
        })),
        contentIssues: z.array(z.string()),
      }),
    });

    // Timeline Manager Agent
    this.agents.set("TimelineManager", {
      id: "timeline_manager",
      type: "TimelineManager",
      expertise: ["deadline management", "scheduling optimization", "time-sensitive insights"],
      systemPrompt: `You are a Timeline Manager focusing on time-sensitive insights and deadline management.
Your task is to analyze dates, deadlines, and schedules to identify:
1. Approaching or critical deadlines
2. Schedule conflicts or bottlenecks
3. Optimized scheduling recommendations
4. Time-related risks or issues

Provide specific, actionable insights for better time management.`,
      outputSchema: z.object({
        criticalDeadlines: z.array(z.object({
          deadline: z.string(),
          dueDate: z.string(),
          daysRemaining: z.number(),
          priority: z.number(),
        })),
        scheduleIssues: z.array(z.string()),
        recommendations: z.array(z.string()),
        timeRisks: z.array(z.object({
          risk: z.string(),
          impact: z.string(),
          mitigation: z.string(),
        })),
      }),
    });

    // Action Recommender Agent
    this.agents.set("ActionRecommender", {
      id: "action_recommender",
      type: "ActionRecommender",
      expertise: ["action prioritization", "workflow optimization", "task assignment"],
      systemPrompt: `You are an Action Recommender specializing in suggesting practical next steps.
Your task is to analyze insights and context to recommend:
1. Specific, actionable tasks for the legal team
2. Prioritized actions based on impact and urgency
3. Workflow optimizations and efficiency improvements
4. Bi-directional integrations with case management, document systems, or calendars

Make recommendations that are specific, achievable, and clearly valuable.`,
      outputSchema: z.object({
        priorityActions: z.array(z.object({
          action: z.string(),
          reason: z.string(),
          priority: z.number(),
          integrationType: z.string(),
        })),
        workflowSuggestions: z.array(z.string()),
        systemIntegrations: z.array(z.object({
          system: z.string(),
          action: z.string(),
          data: z.record(z.string(), z.any()),
        })),
      }),
    });
  }

  /**
   * Execute a specific agent in the swarm
   */
  async executeAgent(
    agentType: SwarmAgentType,
    state: StateManager<InsightGraphState>,
    context: any = {}
  ): Promise<any> {
    const agentConfig = this.agents.get(agentType);
    if (!agentConfig) {
      throw new Error(`Agent type ${agentType} not found in swarm`);
    }

    const workState = state.getState();
    const activities = workState.activities || [];
    const relatedEntity = workState.relatedEntity;
    const source = workState.source;
    const previousAgentOutputs = workState.agentOutputs || {};

    // Create agent-specific outputParser
    const outputParser = StructuredOutputParser.fromZodSchema(agentConfig.outputSchema);

    // Pre-process data based on agent type
    let enhancedContext = { ...context };

    try {
      // Agent-specific pre-processing
      if (agentType === "ActivityAnalyzer" && activities.length > 0) {
        enhancedContext = {
          ...enhancedContext,
          ...await this.prepareActivityData(activities, relatedEntity)
        };
      } else if (agentType === "CaseSpecialist" && relatedEntity?.type === EntityType.Case) {
        enhancedContext = {
          ...enhancedContext,
          ...await this.prepareCaseData(activities, relatedEntity)
        };
      }
    } catch (error) {
      console.error(`Error in data pre-processing for ${agentType}:`, error);
      // Continue with original context if pre-processing fails
    }

    // Construct agent prompt
    const prompt = [
      new SystemMessage(agentConfig.systemPrompt),

      new HumanMessage(`I need your expertise as a ${agentType} to analyze the following data:

Source: ${source}
${activities.length > 0
  ? `Activities: ${JSON.stringify(activities.slice(0, 5))}${activities.length > 5 ? `... and ${activities.length - 5} more` : ''}`
  : ''}
${relatedEntity
  ? `Related Entity: ${JSON.stringify(relatedEntity)}`
  : ''}

Enhanced Context: ${JSON.stringify(enhancedContext)}

${Object.keys(previousAgentOutputs).length > 0
  ? `Outputs from other agents:
${Object.entries(previousAgentOutputs)
  .filter(([key]) => key !== agentConfig.id)
  .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)
  .join('\n')}`
  : 'You are the first agent in the workflow.'}

${outputParser.getFormatInstructions()}

Please analyze this data and provide insights according to your expertise.`),
    ];

    // Execute agent and parse structured output
    try {
      const response = await this.llm.invoke(prompt);
      const parsedOutput = await outputParser.parse(response.content);

      // Update state with agent output
      const currentOutputs = { ...workState.agentOutputs };
      currentOutputs[agentConfig.id] = parsedOutput;

      state.updateState({
        agentOutputs: currentOutputs,
        systemPerformance: {
          ...workState.systemPerformance,
          agentIterations: {
            ...workState.systemPerformance.agentIterations,
            [agentConfig.id]: (workState.systemPerformance.agentIterations[agentConfig.id] || 0) + 1,
          },
        },
      });

      return parsedOutput;
    } catch (error) {
      console.error(`Error executing ${agentType} agent:`, error);

      // Update state with error
      state.updateState({
        errors: [...workState.errors, `${agentType} agent error: ${error}`],
      });

      // Return empty result matching schema shape
      return this.getEmptyOutput(agentConfig.outputSchema);
    }
  }

  /**
   * Prepare document-specific data for the DocumentAnalyzer agent
   */
  private async prepareDocumentData(activities: Activity[], documentEntity: any): Promise<any> {
    // Find activities related to this document
    const documentActivities = activities.filter(activity =>
      activity.documentId === documentEntity.id ||
      (activity.metadata?.documentId === documentEntity.id)
    );

    // Analyze document activities
    const activityTypes: Record<string, number> = {};
    documentActivities.forEach(activity => {
      const type = activity.type || 'unknown';
      activityTypes[type] = (activityTypes[type] || 0) + 1;
    });

    // Get document viewing statistics
    const viewEvents = documentActivities.filter(activity =>
      activity.type === 'document_view' ||
      activity.type === 'document_open'
    );

    // Get document editing statistics
    const editEvents = documentActivities.filter(activity =>
      activity.type === 'document_edit' ||
      activity.type === 'document_update'
    );

    // Get document sharing statistics
    const shareEvents = documentActivities.filter(activity =>
      activity.type === 'document_share'
    );

    // Calculate document statistics
    const documentStats = {
      totalInteractions: documentActivities.length,
      views: viewEvents.length,
      edits: editEvents.length,
      shares: shareEvents.length,
      lastInteractionDate: documentActivities.length > 0 ?
        new Date(Math.max(...documentActivities
          .filter(a => a.timestamp)
          .map(a => new Date(a.timestamp || '').getTime())
        )).toISOString() : null,
    };

    // Find cases related to this document
    const relatedCases = new Set(
      documentActivities
        .filter(activity => activity.caseId)
        .map(activity => activity.caseId)
    );

    return {
      documentInfo: documentEntity,
      documentStats,
      relatedCases: Array.from(relatedCases),
      // In future: Fetch additional document content and metadata from storage/database
    };
  }

  /**
   * Generate empty output matching schema shape for error handling
   */
  /**
   * Prepare timeline-specific data for the TimelineManager agent
   */
  private async prepareTimelineData(activities: Activity[], relatedEntity: any): Promise<any> {
    // Extract all dates and deadlines from activities
    const deadlineDates = new Set<string>();
    const importantDates = new Set<string>();

    // Find activities with deadline information
    activities.forEach(activity => {
      // Check for deadline-related activities
      if (activity.type?.includes('deadline') ||
          activity.type?.includes('due_date') ||
          activity.type?.includes('court_date')) {
        if (activity.metadata?.date) {
          deadlineDates.add(String(activity.metadata.date));
        }
        if (activity.metadata?.deadlineDate) {
          deadlineDates.add(String(activity.metadata.deadlineDate));
        }
      }

      // Check for date mentions in description
      if (activity.description) {
        // Simple date extraction - production would use more sophisticated methods
        const dateMatches = activity.description.match(/(\d{1,2}\/\d{1,2}\/\d{2,4}|\d{4}-\d{2}-\d{2})/g);
        if (dateMatches) {
          dateMatches.forEach(date => importantDates.add(date));
        }
      }
    });

    // Calculate upcoming deadlines
    const today = new Date();
    const upcomingDeadlines = Array.from(deadlineDates)
      .map(dateString => {
        const date = new Date(dateString);
        const daysUntil = Math.ceil((date.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        return { date: dateString, daysUntil };
      })
      .filter(deadline => deadline.daysUntil > -1) // Include today and future dates
      .sort((a, b) => a.daysUntil - b.daysUntil); // Sort by closest first

    // Find activities with schedule conflicts
    // This is a simplified check for overlapping activities on the same day
    const activitiesByDate: Record<string, Activity[]> = {};
    activities
      .filter(a => a.timestamp)
      .forEach(activity => {
        if (!activity.timestamp) return;
        const date = activity.timestamp.split('T')[0];
        if (!activitiesByDate[date]) {
          activitiesByDate[date] = [];
        }
        activitiesByDate[date].push(activity);
      });

    // Find dates with many activities (potential bottlenecks)
    const busyDates = Object.entries(activitiesByDate)
      .filter(([_, acts]) => acts.length >= 3) // Days with 3+ activities
      .map(([date, acts]) => ({
        date,
        activityCount: acts.length,
        activityTypes: Array.from(new Set(acts.map(a => a.type || 'unknown')))
      }))
      .sort((a, b) => b.activityCount - a.activityCount); // Sort by most activities first

    return {
      upcomingDeadlines,
      busyDates,
      allDeadlineDates: Array.from(deadlineDates),
      importantDateMentions: Array.from(importantDates),
    };
  }

  private getEmptyOutput(schema: z.ZodType<any>): any {
    // Safe way to get shape without assuming _def.shape() exists
    let emptyOutput: Record<string, unknown> = {};

    try {
      // Try to extract schema shape if available
      const schemaObj = schema as any;
      const shape = schemaObj._def?.shape ? schemaObj._def.shape() : null;

      if (shape) {
        for (const [key, value] of Object.entries(shape)) {
          if (value instanceof z.ZodArray) {
            emptyOutput[key] = [];
          } else if (value instanceof z.ZodObject) {
            emptyOutput[key] = this.getEmptyOutput(value);
          } else if (value instanceof z.ZodRecord) {
            emptyOutput[key] = {};
          } else {
            emptyOutput[key] = null;
          }
        }
      } else {
        // Fallback to a basic empty object if shape isn't available
        emptyOutput = {};
      }
    } catch (error) {
      console.error("Error generating empty output:", error);
      emptyOutput = {};
    }

    return emptyOutput;
  }

  /**
   * Pre-process activities data for the ActivityAnalyzer agent
   */
  private async prepareActivityData(activities: Activity[], relatedEntity: any): Promise<any> {
    // Group activities by type
    const groupedByType: Record<string, Activity[]> = {};

    activities.forEach(activity => {
      const type = activity.type || 'unknown';
      if (!groupedByType[type]) {
        groupedByType[type] = [];
      }
      groupedByType[type].push(activity);
    });

    // Find timeline patterns (activities over time)
    const timelineData = this.analyzeTimelinePatterns(activities);

    // Find entity relationships in activities
    const entityRelationships = this.extractEntityRelationships(activities);

    // Enhance with Neo4j data if available
    // This is a placeholder for future Neo4j integration
    const enhancedContext = relatedEntity ? {
      ...relatedEntity,
      // In the future, retrieve additional context from Neo4j here
      previousInsights: [],
      relatedActivities: [],
    } : null;

    return {
      groupedByType,
      timelineData,
      entityRelationships,
      enhancedContext
    };
  }

  /**
   * Analyze timeline patterns from activities
   */
  private analyzeTimelinePatterns(activities: Activity[]): any {
    // Sort activities by timestamp
    const sortedActivities = [...activities].sort((a, b) => {
      return new Date(a.timestamp || '').getTime() - new Date(b.timestamp || '').getTime();
    });

    // Group by day for frequency analysis
    const activityByDay: Record<string, number> = {};
    sortedActivities.forEach(activity => {
      if (!activity.timestamp) return;

      const date = new Date(activity.timestamp).toISOString().split('T')[0];
      activityByDay[date] = (activityByDay[date] || 0) + 1;
    });

    // Calculate frequency metrics
    const dates = Object.keys(activityByDay);
    const counts = Object.values(activityByDay);

    return {
      dailyFrequency: activityByDay,
      totalDays: dates.length,
      averagePerDay: counts.length > 0 ? counts.reduce((a, b) => a + b, 0) / counts.length : 0,
      mostActiveDay: dates.length > 0 ?
        dates.reduce((a, b) => activityByDay[a] > activityByDay[b] ? a : b) : null,
      leastActiveDay: dates.length > 0 ?
        dates.reduce((a, b) => activityByDay[a] < activityByDay[b] ? a : b) : null,
    };
  }

  /**
   * Prepare case-specific data for the CaseSpecialist agent
   */
  private async prepareCaseData(activities: Activity[], caseEntity: any): Promise<any> {
    // Case timeline analysis (activities related to this case)
    const caseActivities = activities.filter(activity =>
      activity.caseId === caseEntity.id ||
      ((activity.metadata as any)?.caseId === caseEntity.id)
    );

    // Group activities by type within this case
    const activityTypes: Record<string, number> = {};
    caseActivities.forEach(activity => {
      const type = activity.type || 'unknown';
      activityTypes[type] = (activityTypes[type] || 0) + 1;
    });

    // Calculate case activity statistics
    const caseStats = {
      totalActivities: caseActivities.length,
      activityBreakdown: activityTypes,
      lastActivityDate: caseActivities.length > 0 ?
        new Date(Math.max(...caseActivities
          .filter(a => a.timestamp)
          .map(a => new Date(a.timestamp || '').getTime())
        )).toISOString() : null,
      firstActivityDate: caseActivities.length > 0 ?
        new Date(Math.min(...caseActivities
          .filter(a => a.timestamp)
          .map(a => new Date(a.timestamp || '').getTime())
        )).toISOString() : null,
    };

    // Identify related documents for this case
    const relatedDocuments = activities
      .filter(activity =>
        activity.caseId === caseEntity.id &&
        activity.documentId
      )
      .map(activity => activity.documentId);

    // Find unique document IDs
    const uniqueDocuments = Array.from(new Set(relatedDocuments));

    return {
      caseInfo: caseEntity,
      caseStats,
      uniqueDocuments,
      // In future: Fetch additional case data from Neo4j or database
    };
  }

  /**
   * Extract entity relationships from activities
   */
  private extractEntityRelationships(activities: Activity[]): any {
    const entityMentions: Record<string, Set<string>> = {
      cases: new Set(),
      documents: new Set(),
      people: new Set(),
      deadlines: new Set(),
    };

    // Extract entity mentions from activities
    activities.forEach(activity => {
      // Extract case mentions
      if (activity.caseId) {
        entityMentions.cases.add(activity.caseId);
      }

      // Extract document mentions
      if (activity.documentId) {
        entityMentions.documents.add(activity.documentId);
      }

      // Extract people mentions (simplified approach)
      if (activity.description) {
        // This is a placeholder for more sophisticated entity extraction
        // In a production system, use NER or similar techniques
      }
    });

    // Convert Sets to Arrays for JSON serialization
    return {
      cases: Array.from(entityMentions.cases),
      documents: Array.from(entityMentions.documents),
      people: Array.from(entityMentions.people),
      deadlines: Array.from(entityMentions.deadlines),
    };
  }

  /**
   * Request collaboration from another agent to answer a specific question
   */
  async requestAgentCollaboration(
    state: StateManager<InsightGraphState>,
    fromAgent: string,
    toAgent: string,
    question: string
  ): Promise<string> {
    if (!this.collaborationEnabled) {
      return "Collaboration is disabled";
    }

    // Initialize collaboration structure if needed
    if (!state.getState().agentCollaboration) {
      state.updateState({
        agentCollaboration: {
          requests: [],
          sharedContext: {},
          votingResults: {}
        }
      });
    }

    const workState = state.getState();
    const collaboration = workState.agentCollaboration!;

    // Check if maximum requests limit has been reached
    const existingRequests = collaboration.requests.filter(r => r.fromAgent === fromAgent);
    if (existingRequests.length >= this.maxCollaborationRequests) {
      return `Request limit reached for agent ${fromAgent}`;
    }

    // Get target agent configuration
    const targetAgent = this.agents.get(toAgent);
    if (!targetAgent) {
      return `Agent ${toAgent} not found`;
    }

    // Prepare context for the target agent
    const agentContext = {
      fromAgent,
      questionContext: workState.agentOutputs[fromAgent] || {},
      relatedEntity: workState.relatedEntity,
      sharedContext: collaboration.sharedContext || {}
    };

    // Construct prompt for the target agent
    const prompt = [
      new SystemMessage(`You are a ${targetAgent.type} agent specializing in ${targetAgent.expertise.join(', ')}.
      Another agent (${fromAgent}) has requested your expertise on a specific question.
      Provide a concise, accurate, and helpful response based on your specialization.
      Remember that your response will be used by the requesting agent to enhance its own analysis.`),

      new HumanMessage(`Question from ${fromAgent} agent: ${question}

      Context information:
      ${JSON.stringify(agentContext, null, 2)}

      Please provide your expert response to help the ${fromAgent} agent.`),
    ];

    try {
      // Generate response from the target agent
      const response = await this.llm.invoke(prompt);
      const responseText = response.content.toString();

      // Record the collaboration interaction
      const collaborationRequest = {
        fromAgent,
        toAgent,
        question,
        response: responseText,
        timestamp: new Date().toISOString()
      };

      // Update state with the collaboration request
      state.updateState({
        agentCollaboration: {
          ...collaboration,
          requests: [...collaboration.requests, collaborationRequest]
        }
      });

      // Store in collaboration memory for future reference
      if (!this.collaborationMemory.has(fromAgent)) {
        this.collaborationMemory.set(fromAgent, []);
      }
      this.collaborationMemory.get(fromAgent)!.push({
        question,
        response: responseText
      });

      return responseText;
    } catch (error) {
      console.error(`Error in agent collaboration between ${fromAgent} and ${toAgent}:`, error);
      return `Collaboration failed: ${error}`;
    }
  }

  /**
   * Process all collaboration requests in the current state
   */
  async processCollaborationRequests(state: StateManager<InsightGraphState>): Promise<void> {
    const workState = state.getState();
    if (!workState.agentCollaboration || workState.agentCollaboration.requests.length === 0) {
      return;
    }

    // Process any pending collaboration requests (those without responses)
    const pendingRequests = workState.agentCollaboration.requests.filter(req => !req.response);
    for (const request of pendingRequests) {
      const response = await this.requestAgentCollaboration(
        state,
        request.fromAgent,
        request.toAgent,
        request.question
      );

      // Update the request with the response
      const updatedRequests = workState.agentCollaboration.requests.map(req => {
        if (req.fromAgent === request.fromAgent &&
            req.toAgent === request.toAgent &&
            req.question === request.question &&
            !req.response) {
          return { ...req, response };
        }
        return req;
      });

      // Update state with the processed request
      state.updateState({
        agentCollaboration: {
          ...workState.agentCollaboration,
          requests: updatedRequests
        }
      });
    }
  }

  /**
   * Enhance context with external knowledge and legal information
   */
  async enhanceContextWithExternalKnowledge(
    state: StateManager<InsightGraphState>
  ): Promise<void> {
    const workState = state.getState();

    // Initialize enhanced context if needed
    if (!workState.enhancedContext) {
      state.updateState({
        enhancedContext: {
          entities: {},
          relationshipGraph: {},
          relevanceScores: {},
          externalKnowledge: {}
        }
      });
    }

    // Extract entities from activities and related entity
    const entities: Record<string, unknown> = {};
    const relationshipGraph: Record<string, string[]> = {};

    // Extract entities from activities
    if (workState.activities && workState.activities.length > 0) {
      const extractedEntities = this.extractEntityRelationships(workState.activities);

      // Process cases
      extractedEntities.cases.forEach((caseId: string) => {
        entities[`case_${caseId}`] = { type: 'case', id: caseId };
        relationshipGraph[`case_${caseId}`] = [];
      });

      // Process documents
      extractedEntities.documents.forEach((docId: string) => {
        entities[`document_${docId}`] = { type: 'document', id: docId };
        relationshipGraph[`document_${docId}`] = [];
      });

      // Build relationships
      workState.activities.forEach(activity => {
        if (activity.caseId && activity.documentId) {
          const caseKey = `case_${activity.caseId}`;
          const docKey = `document_${activity.documentId}`;

          if (!relationshipGraph[caseKey].includes(docKey)) {
            relationshipGraph[caseKey].push(docKey);
          }

          if (!relationshipGraph[docKey].includes(caseKey)) {
            relationshipGraph[docKey].push(caseKey);
          }
        }
      });
    }

    // Add the main related entity if present
    if (workState.relatedEntity) {
      const entityId = workState.relatedEntity.id;
      const entityType = workState.relatedEntity.type;
      const entityKey = `${entityType}_${entityId}`;

      entities[entityKey] = {
        ...workState.relatedEntity,
        relevance: 1.0 // Maximum relevance
      };

      // Ensure it has an entry in the relationship graph
      if (!relationshipGraph[entityKey]) {
        relationshipGraph[entityKey] = [];
      }
    }

    // Calculate relevance scores based on connection to the main entity
    const relevanceScores: Record<string, number> = {};
    Object.keys(entities).forEach(entityKey => {
      // Main entity gets maximum relevance
      if (workState.relatedEntity &&
          entityKey === `${workState.relatedEntity.type}_${workState.relatedEntity.id}`) {
        relevanceScores[entityKey] = 1.0;
      }
      // Direct connections get high relevance
      else if (workState.relatedEntity &&
               relationshipGraph[`${workState.relatedEntity.type}_${workState.relatedEntity.id}`] &&
               relationshipGraph[`${workState.relatedEntity.type}_${workState.relatedEntity.id}`].includes(entityKey)) {
        relevanceScores[entityKey] = 0.8;
      }
      // Secondary connections get medium relevance
      else if (workState.relatedEntity) {
        relevanceScores[entityKey] = 0.5;
      }
      // Otherwise low default relevance
      else {
        relevanceScores[entityKey] = 0.3;
      }
    });

    // Update the state with enhanced context
    state.updateState({
      enhancedContext: {
        entities,
        relationshipGraph,
        relevanceScores,
        externalKnowledge: workState.enhancedContext?.externalKnowledge || {}
      }
    });
  }

  /**
   * Implement voting mechanism for agents to resolve conflicting insights
   */
  async resolveConflictsWithVoting(
    state: StateManager<InsightGraphState>,
    conflictDescription: string,
    options: string[]
  ): Promise<string> {
    if (!this.collaborationEnabled || options.length === 0) {
      return options[0] || "No resolution available";
    }

    // Initialize collaboration structure if needed
    if (!state.getState().agentCollaboration) {
      state.updateState({
        agentCollaboration: {
          requests: [],
          sharedContext: {},
          votingResults: {}
        }
      });
    }

    const votes: Record<string, number> = {};
    options.forEach(option => { votes[option] = 0; });

    // Have each agent vote on the options
    for (const [agentId, agentConfig] of this.agents.entries()) {
      // Construct prompt for the agent to vote
      const prompt = [
        new SystemMessage(`You are a ${agentConfig.type} agent specializing in ${agentConfig.expertise.join(', ')}.
        You need to vote on a resolution to a conflict between different insights.
        Based on your expertise, select the BEST option from the provided choices.`),

        new HumanMessage(`Conflict: ${conflictDescription}

        Options:
        ${options.map((opt, idx) => `${idx + 1}. ${opt}`).join('\n')}

        Based on your expertise as a ${agentConfig.type} agent, which option do you think is most accurate and useful?
        Respond ONLY with the number of your selection.`),
      ];

      try {
        // Get the agent's vote
        const response = await this.llm.invoke(prompt);
        const voteText = response.content.toString();

        // Parse the vote - looking for a number in the response
        const voteMatch = voteText.match(/\d+/);
        if (voteMatch) {
          const voteIndex = parseInt(voteMatch[0], 10) - 1;
          if (voteIndex >= 0 && voteIndex < options.length) {
            votes[options[voteIndex]] += 1;
          }
        }
      } catch (error) {
        console.error(`Error getting vote from agent ${agentId}:`, error);
      }
    }

    // Find the option with the most votes
    let winningOption = options[0];
    let maxVotes = votes[winningOption] || 0;

    for (const [option, voteCount] of Object.entries(votes)) {
      if (voteCount > maxVotes) {
        winningOption = option;
        maxVotes = voteCount;
      }
    }

    // Update state with voting results
    if (state.getState().agentCollaboration) {
      // Create a simplified object with string keys and number values for the votes
      const votesCopy: Record<string, number> = {};
      Object.keys(votes).forEach(key => {
        votesCopy[key] = votes[key];
      });

      // Store the vote counts in the shared context instead of directly in votingResults
      // to avoid type compatibility issues
      const updatedSharedContext = {
        ...state.getState().agentCollaboration!.sharedContext,
        [`vote_${conflictDescription}`]: votesCopy
      };

      state.updateState({
        agentCollaboration: {
          ...state.getState().agentCollaboration!,
          sharedContext: updatedSharedContext
        }
      });
    }

    return winningOption;
  }

  /**
   * Process agent outputs into unified insights
   */
  async synthesizeInsights(state: StateManager<InsightGraphState>): Promise<Insight[]> {
    const workState = state.getState();
    const agentOutputs = workState.agentOutputs || {};

    if (Object.keys(agentOutputs).length === 0) {
      return [];
    }

    // Process collaboration requests if enabled
    if (this.collaborationEnabled) {
      await this.processCollaborationRequests(state);
    }

    // Enhance context with external knowledge
    await this.enhanceContextWithExternalKnowledge(state);

    // Construct prompt for synthesizing insights
    const prompt = [
      new SystemMessage(`You are an Insight Synthesizer responsible for combining outputs from multiple specialist agents into unified, actionable insights.
Your task is to:
1. Identify the most important findings across all agent outputs
2. Create coherent, non-redundant insights that capture key information
3. Prioritize insights based on importance and actionability
4. Ensure each insight is specific, clear, and valuable

Each insight should have:
- A clear message explaining what was found
- Suggestions for action where appropriate
- A priority score (1-10, higher = more important)
- Related entity information when available`),

      new HumanMessage(`Please synthesize the following agent outputs into unified insights:

Agent Outputs: ${JSON.stringify(agentOutputs)}

Source Type: ${workState.source}
${workState.relatedEntity
  ? `Related Entity: ${JSON.stringify(workState.relatedEntity)}`
  : ''}

Generate a list of insights in JSON format, where each insight has:
- id: A unique string ID
- message: The main insight message
- suggestions: Array of suggested actions
- priority: Number from 1-10 indicating importance
- relatedEntity: Object with type and id if applicable
- timestamp: Current timestamp
- aiGenerated: true
- feedbackId: A unique string for tracking feedback`),
    ];

    // Generate synthesized insights
    try {
      const response = await this.llm.invoke(prompt);
      let insights: Insight[] = [];

      try {
        insights = JSON.parse(response.content);
      } catch (parseError) {
        // If parsing fails, try to extract array portion
        const match = response.content.match(/\[[\s\S]*\]/);
        if (match) {
          insights = JSON.parse(match[0]);
        } else {
          throw new Error("Failed to parse insights from response");
        }
      }

      // Ensure each insight has required fields
      insights = insights.map(insight => ({
        ...insight,
        id: insight.id || `ins_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
        timestamp: insight.timestamp || new Date().toISOString(),
        aiGenerated: true,
        feedbackId: insight.feedbackId || `fb_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
      }));

      // Update state with synthesized insights
      state.updateState({
        generatedInsights: insights,
      });

      return insights;
    } catch (error) {
      console.error("Error synthesizing insights:", error);
      state.updateState({
        errors: [...workState.errors, `Insight synthesis error: ${error}`],
      });
      return [];
    }
  }
}

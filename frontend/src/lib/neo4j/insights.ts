/**
 * Neo4j utilities for storing and retrieving insights
 * Supports bi-directional insights for cases, documents, and activities
 * Includes feedback tracking for continual improvement
 */

import { getDriver } from './driver';
import { v4 as uuidv4 } from 'uuid';

// Check if Neo4j insights are enabled
const ENABLE_NEO4J_INSIGHTS = process.env.ENABLE_NEO4J_INSIGHTS === 'true';

/**
 * Record an insight in Neo4j for future reference and analysis
 *
 * @param insight The insight object to store
 * @param userId The user who the insight is for
 * @param source The source of the insight (e.g., 'activity', 'case', 'document', 'proactive')
 * @param relatedEntity Optional related entity (case, document, etc.)
 * @param additionalContext Optional additional context
 * @returns Boolean indicating success
 */
export async function recordInsightInNeo4j(
  insight: any,
  userId: string,
  source: string,
  relatedEntity: any = null,
  additionalContext: any = {}
): Promise<boolean> {
  // Skip if Neo4j insights are disabled
  if (!ENABLE_NEO4J_INSIGHTS) {
    console.log('Neo4j insights disabled, skipping recordInsightInNeo4j');
    return false;
  }

  const driver = getDriver();
  if (!driver) {
    console.error('Neo4j driver not available');
    return false;
  }

  const session = driver.session();

  try {
    // Ensure insight has an ID
    const insightId = insight.id || `insight_${uuidv4()}`;

    // Prepare timestamp
    const timestamp = insight.timestamp || new Date().toISOString();

    // Convert insight priority to number if it's a string
    const priority = typeof insight.priority === 'string'
      ? parseInt(insight.priority, 10)
      : (insight.priority || 5);

    // Format suggestions as a string if they're an array
    const suggestions = Array.isArray(insight.suggestions)
      ? insight.suggestions.join('; ')
      : (insight.suggestions || '');

    // Base Cypher query for creating an insight
    let query = `
      CREATE (i:Insight {
        id: $insightId,
        message: $message,
        suggestions: $suggestions,
        priority: $priority,
        source: $source,
        timestamp: $timestamp,
        aiGenerated: $aiGenerated,
        feedbackId: $feedbackId
      })
      WITH i
      MATCH (u:User {id: $userId})
      MERGE (i)-[:FOR_USER]->(u)
      MERGE (u)-[:HAS_INSIGHT]->(i)
    `;

    // Parameters for the query
    const params: any = {
      insightId,
      message: insight.message || '',
      suggestions,
      priority,
      source,
      timestamp,
      aiGenerated: insight.aiGenerated === true,
      feedbackId: insight.feedbackId || `feedback_${uuidv4()}`,
      userId,
      additionalContext: JSON.stringify(additionalContext)
    };

    // Add related entity if provided
    if (relatedEntity) {
      params.entityId = relatedEntity.id;
      params.entityType = relatedEntity.type;
      params.entityName = relatedEntity.name || '';

      // Connect insight to the related entity based on its type
      if (relatedEntity.type === 'case') {
        query += `
          WITH i
          MATCH (c:Case {id: $entityId})
          MERGE (i)-[:RELATES_TO_CASE]->(c)
          MERGE (c)-[:HAS_INSIGHT]->(i)
        `;
      } else if (relatedEntity.type === 'document') {
        query += `
          WITH i
          MATCH (d:Document {id: $entityId})
          MERGE (i)-[:RELATES_TO_DOCUMENT]->(d)
          MERGE (d)-[:HAS_INSIGHT]->(i)
        `;
      } else if (relatedEntity.type === 'deadline') {
        query += `
          WITH i
          MATCH (d:Deadline {id: $entityId})
          MERGE (i)-[:RELATES_TO_DEADLINE]->(d)
          MERGE (d)-[:HAS_INSIGHT]->(i)
        `;
      }
    }

    // Store any activities that contributed to this insight
    if (source === 'activity' && Array.isArray(insight.relatedActivities)) {
      params.activityIds = insight.relatedActivities;

      query += `
        WITH i
        UNWIND $activityIds AS activityId
        MATCH (a:Activity {id: activityId})
        MERGE (i)-[:DERIVED_FROM]->(a)
      `;
    }

    // Store additional context
    query += `
      WITH i
      CREATE (c:Context {
        id: $contextId,
        data: $additionalContext,
        timestamp: $timestamp
      })
      MERGE (i)-[:HAS_CONTEXT]->(c)
      RETURN i.id as insightId
    `;

    params.contextId = `context_${uuidv4()}`;

    // Execute the query
    await session.run(query, params);

    return true;
  } catch (error) {
    console.error('Error recording insight in Neo4j:', error);
    return false;
  } finally {
    await session.close();
  }
}

/**
 * Record feedback for an insight in Neo4j
 *
 * @param feedbackId The feedback ID (from the insight)
 * @param rating User rating (1-5)
 * @param comment Optional user comment
 * @param userId User ID providing the feedback
 * @returns Boolean indicating success
 */
export async function recordInsightFeedback(
  feedbackId: string,
  rating: number,
  comment: string = '',
  userId: string
): Promise<boolean> {
  // Skip if Neo4j insights are disabled
  if (!ENABLE_NEO4J_INSIGHTS) {
    console.log('Neo4j insights disabled, skipping recordInsightFeedback');
    return false;
  }

  const driver = getDriver();
  if (!driver) {
    console.error('Neo4j driver not available');
    return false;
  }

  const session = driver.session();

  try {
    // Validate rating
    const validRating = Math.min(Math.max(Math.round(rating), 1), 5);

    // Create feedback node and link it to the insight
    const query = `
      MATCH (i:Insight {feedbackId: $feedbackId})
      MATCH (u:User {id: $userId})
      CREATE (f:Feedback {
        id: $id,
        rating: $rating,
        comment: $comment,
        timestamp: $timestamp
      })
      MERGE (f)-[:FOR_INSIGHT]->(i)
      MERGE (f)-[:FROM_USER]->(u)
      MERGE (i)-[:HAS_FEEDBACK]->(f)
      RETURN f.id as feedbackId
    `;

    const params = {
      feedbackId,
      id: `fb_${uuidv4()}`,
      rating: validRating,
      comment,
      userId,
      timestamp: new Date().toISOString()
    };

    // Execute the query
    await session.run(query, params);

    // Update the user's preference model with this feedback
    await updateUserPreferenceModel(userId, feedbackId, validRating);

    return true;
  } catch (error) {
    console.error('Error recording insight feedback in Neo4j:', error);
    return false;
  } finally {
    await session.close();
  }
}

/**
 * Update user preference model based on feedback
 * This builds a model of what types of insights the user finds valuable
 *
 * @param userId User ID
 * @param feedbackId Feedback ID (links to insight)
 * @param rating User rating (1-5)
 */
async function updateUserPreferenceModel(
  userId: string,
  feedbackId: string,
  rating: number
): Promise<void> {
  // Skip if Neo4j insights are disabled
  if (!ENABLE_NEO4J_INSIGHTS) {
    return;
  }

  const driver = getDriver();
  if (!driver) {
    console.error('Neo4j driver not available');
    return;
  }

  const session = driver.session();

  try {
    // Extract insight characteristics and update user preferences
    const query = `
      // Find the insight and its characteristics
      MATCH (i:Insight {feedbackId: $feedbackId})
      MATCH (u:User {id: $userId})

      // Get insight source and priority
      WITH i, u, i.source as source, i.priority as priority

      // Find or create the user preference model
      MERGE (p:PreferenceModel {userId: $userId})

      // Create or update relationships for source preferences
      MERGE (p)-[r:PREFERS_SOURCE]->(s:InsightSource {name: source})
      ON CREATE SET r.count = 1, r.totalRating = $rating, r.avgRating = $rating
      ON MATCH SET r.count = r.count + 1, r.totalRating = r.totalRating + $rating,
                 r.avgRating = toFloat(r.totalRating + $rating) / (r.count + 1)

      // Create or update relationships for priority preferences
      MERGE (p)-[rp:PREFERS_PRIORITY]->(prio:InsightPriority {level: toString(priority)})
      ON CREATE SET rp.count = 1, rp.totalRating = $rating, rp.avgRating = $rating
      ON MATCH SET rp.count = rp.count + 1, rp.totalRating = rp.totalRating + $rating,
                  rp.avgRating = toFloat(rp.totalRating + $rating) / (rp.count + 1)

      // If the insight is related to a case, update case type preferences
      OPTIONAL MATCH (i)-[:RELATES_TO_CASE]->(c:Case)
      WITH p, i, u, c
      WHERE c IS NOT NULL
      MERGE (p)-[rc:PREFERS_CASE_TYPE]->(ct:CaseType {type: c.type})
      ON CREATE SET rc.count = 1, rc.totalRating = $rating, rc.avgRating = $rating
      ON MATCH SET rc.count = rc.count + 1, rc.totalRating = rc.totalRating + $rating,
                  rc.avgRating = toFloat(rc.totalRating + $rating) / (rc.count + 1)

      RETURN p.userId as userId
    `;

    const params = {
      userId,
      feedbackId,
      rating
    };

    // Execute the query
    await session.run(query, params);
  } catch (error) {
    console.error('Error updating user preference model in Neo4j:', error);
  } finally {
    await session.close();
  }
}

/**
 * Get recent insights for a user
 *
 * @param userId User ID
 * @param limit Maximum number of insights to retrieve
 * @param source Optional source filter
 * @returns Array of insight objects
 */
export async function getRecentInsights(
  userId: string,
  limit: number = 10,
  source?: string
): Promise<any[]> {
  // Skip if Neo4j insights are disabled
  if (!ENABLE_NEO4J_INSIGHTS) {
    console.log('Neo4j insights disabled, returning empty insights array');
    return [];
  }

  const driver = getDriver();
  if (!driver) {
    console.error('Neo4j driver not available');
    return [];
  }

  const session = driver.session();

  try {
    // Base query to get insights for the user
    let query = `
      MATCH (i:Insight)-[:FOR_USER]->(u:User {id: $userId})
    `;

    // Add source filter if provided
    if (source) {
      query += `WHERE i.source = $source`;
    }

    // Complete the query with sorting, limiting, and return
    query += `
      // Get any feedback that exists
      OPTIONAL MATCH (i)<-[:FOR_INSIGHT]-(f:Feedback)

      // Get any related entities
      OPTIONAL MATCH (i)-[:RELATES_TO_CASE]->(c:Case)
      OPTIONAL MATCH (i)-[:RELATES_TO_DOCUMENT]->(d:Document)
      OPTIONAL MATCH (i)-[:RELATES_TO_DEADLINE]->(dl:Deadline)

      RETURN i.id as id,
             i.message as message,
             i.suggestions as suggestions,
             i.priority as priority,
             i.source as source,
             i.timestamp as timestamp,
             i.aiGenerated as aiGenerated,
             i.feedbackId as feedbackId,
             f.rating as feedback,
             f.comment as feedbackComment,
             CASE
               WHEN c IS NOT NULL THEN 'case'
               WHEN d IS NOT NULL THEN 'document'
               WHEN dl IS NOT NULL THEN 'deadline'
               ELSE null
             END as relatedEntityType,
             CASE
               WHEN c IS NOT NULL THEN c.id
               WHEN d IS NOT NULL THEN d.id
               WHEN dl IS NOT NULL THEN dl.id
               ELSE null
             END as relatedEntityId,
             CASE
               WHEN c IS NOT NULL THEN c.name
               WHEN d IS NOT NULL THEN d.name
               WHEN dl IS NOT NULL THEN dl.title
               ELSE null
             END as relatedEntityName
      ORDER BY i.timestamp DESC
      LIMIT $limit
    `;

    const params: any = {
      userId,
      limit: parseInt(String(limit), 10)
    };

    if (source) {
      params.source = source;
    }

    // Execute the query
    const result = await session.run(query, params);

    // Transform records into insight objects
    return result.records.map(record => {
      const insight = {
        id: record.get('id'),
        message: record.get('message'),
        suggestions: record.get('suggestions'),
        priority: record.get('priority'),
        source: record.get('source'),
        timestamp: record.get('timestamp'),
        aiGenerated: record.get('aiGenerated'),
        feedbackId: record.get('feedbackId'),
        feedback: record.get('feedback'),
        feedbackComment: record.get('feedbackComment'),
      };

      // Add related entity if it exists
      const relatedEntityType = record.get('relatedEntityType');
      const relatedEntityId = record.get('relatedEntityId');

      if (relatedEntityType && relatedEntityId) {
        (insight as any).relatedEntity = {
          type: relatedEntityType,
          id: relatedEntityId,
          name: record.get('relatedEntityName')
        };
      }

      return insight;
    });
  } catch (error) {
    console.error('Error getting recent insights from Neo4j:', error);
    return [];
  } finally {
    await session.close();
  }
}

/**
 * Get insight preferences for a user
 * Used to tailor future insights based on what the user finds valuable
 *
 * @param userId User ID
 * @returns User preference model
 */
export async function getUserInsightPreferences(userId: string): Promise<any> {
  // Skip if Neo4j insights are disabled
  if (!ENABLE_NEO4J_INSIGHTS) {
    console.log('Neo4j insights disabled, returning empty preference model');
    return { sources: {}, priorities: {}, caseTypes: {} };
  }

  const driver = getDriver();
  if (!driver) {
    console.error('Neo4j driver not available');
    return { sources: {}, priorities: {}, caseTypes: {} };
  }

  const session = driver.session();

  try {
    // Query to get user preferences
    const query = `
      // Find the user's preference model
      MATCH (p:PreferenceModel {userId: $userId})

      // Get source preferences
      OPTIONAL MATCH (p)-[rs:PREFERS_SOURCE]->(s:InsightSource)
      WITH p, collect({name: s.name, avgRating: rs.avgRating, count: rs.count}) as sources

      // Get priority preferences
      OPTIONAL MATCH (p)-[rp:PREFERS_PRIORITY]->(prio:InsightPriority)
      WITH p, sources, collect({level: prio.level, avgRating: rp.avgRating, count: rp.count}) as priorities

      // Get case type preferences
      OPTIONAL MATCH (p)-[rc:PREFERS_CASE_TYPE]->(ct:CaseType)
      WITH sources, priorities, collect({type: ct.type, avgRating: rc.avgRating, count: rc.count}) as caseTypes

      RETURN sources, priorities, caseTypes
    `;

    const params = {
      userId
    };

    // Execute the query
    const result = await session.run(query, params);

    // If no preference model exists, return empty defaults
    if (result.records.length === 0) {
      return { sources: {}, priorities: {}, caseTypes: {} };
    }

    // Transform records into a preference model
    const record = result.records[0];

    // Process source preferences
    const sources: Record<string, unknown> = {};
    const sourceNodes = record.get('sources');
    sourceNodes.forEach((node: any) => {
      sources[node.name] = {
        avgRating: node.avgRating,
        count: node.count
      };
    });

    // Process priority preferences
    const priorities: Record<string, unknown> = {};
    const priorityNodes = record.get('priorities');
    priorityNodes.forEach((node: any) => {
      priorities[node.level] = {
        avgRating: node.avgRating,
        count: node.count
      };
    });

    // Process case type preferences
    const caseTypes: Record<string, unknown> = {};
    const caseTypeNodes = record.get('caseTypes');
    caseTypeNodes.forEach((node: any) => {
      caseTypes[node.type] = {
        avgRating: node.avgRating,
        count: node.count
      };
    });

    return {
      sources,
      priorities,
      caseTypes
    };
  } catch (error) {
    console.error('Error getting user insight preferences from Neo4j:', error);
    return { sources: {}, priorities: {}, caseTypes: {} };
  } finally {
    await session.close();
  }
}

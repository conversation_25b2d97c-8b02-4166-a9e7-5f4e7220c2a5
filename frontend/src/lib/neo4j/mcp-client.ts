/**
 * Neo4j MCP Client
 *
 * Handles communication with the Neo4j MCP server
 */

type Neo4jMcpResponse = {
  data?: any;
  error?: string;
};

/**
 * Execute a Cypher query (read-only) on the Neo4j MCP server
 *
 * @param query The Cypher query to execute
 * @param params Parameters for the query
 * @returns Promise resolving to query results
 */
export async function executeCypherQuery(query: string, params: Record<string, unknown> = {}): Promise<Neo4jMcpResponse> {
  try {
    // This is a simplified example - you might need to adjust the request format
    // based on your MCP server's expectations
    // Use absolute URL with origin to avoid URL parsing issues
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3001';
    const response = await fetch(`${baseUrl}/api/mcp/neo4j/read`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        params
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Neo4j MCP request failed with status ${response.status}: ${errorText}`);
    }

    const result = await response.json();
    return { data: result };
  } catch (error: any) {
    console.error('Neo4j MCP read request failed:', error);
    return { error: error.message || 'Unknown error executing Cypher query' };
  }
}

/**
 * Execute a Cypher write query on the Neo4j MCP server
 *
 * @param query The Cypher query to execute
 * @param params Parameters for the query
 * @returns Promise resolving to query results
 */
export async function executeCypherWrite(query: string, params: Record<string, unknown> = {}): Promise<Neo4jMcpResponse> {
  try {
    // This is a simplified example - you might need to adjust the request format
    // based on your MCP server's expectations
    // Use absolute URL with origin to avoid URL parsing issues
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3001';
    const response = await fetch(`${baseUrl}/api/mcp/neo4j/write`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        params
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Neo4j MCP write request failed with status ${response.status}: ${errorText}`);
    }

    const result = await response.json();
    return { data: result };
  } catch (error: any) {
    console.error('Neo4j MCP write request failed:', error);
    return { error: error.message || 'Unknown error executing Cypher write query' };
  }
}

import { Insight } from '@/lib/types';
import { llmService } from '@/lib/llm/service';

// Configure environment variables for LLM service
const OPENAI_MODEL = process.env.OPENAI_MODEL || 'gpt-4o';
const GOOGLE_MODEL = process.env.GOOGLE_MODEL || 'gemini-1.5-flash';

/**
 * Analyzes a set of activities to generate intelligent insights
 * @param activities Array of user activities to analyze
 * @param options Configuration options for the analysis
 * @returns Enhanced insights with semantic understanding
 */
export async function analyzeActivities(activities: unknown[], options: {
  useCache?: boolean;
  forceProvider?: 'openai' | 'google';
} = {}) {
  if (!activities || activities.length === 0) {
    return [];
  }

  const { useCache = true, forceProvider } = options;

  try {
    // Prepare activities for analysis
    const activityData = activities.map(a => ({
      id: (a as any).activityId,
      action: (a as any).action,
      category: (a as any).category,
      summary: (a as any).summary,
      importance: (a as any).importance,
      time: (a as any).time,
      caseTitle: (a as any).caseTitle,
      documentName: (a as any).documentName,
      tags: (a as any).tags || [],
    }));

    // System prompt for activity analysis
    const systemPrompt = `You are an AI assistant for a legal practice management system.
    Analyze the following user activities and generate actionable insights.
    Group related activities, identify priorities, and suggest next actions.
    Focus on deadlines, case-related activities, and important documents.`;

    // User prompt with activity data
    const userPrompt = `Analyze these activities and generate insights: ${JSON.stringify(activityData)}

    Return a JSON array of insights with the following structure:
    [
      {
        "id": "unique-id", // can use original activity ID or generate new
        "message": "Insight message that summarizes the activity or group of activities",
        "suggestions": ["Suggestion 1", "Suggestion 2"], // 1-3 actionable suggestions
        "priority": 10, // 1-10 with 10 being highest
        "groupKey": "case-123", // optional grouping key (case name, document, etc.)
        "relatedActivities": ["activity-id-1", "activity-id-2"], // IDs of related activities
        "relatedEntity": { // optional related entity
          "type": "case|document|client|etc",
          "name": "Entity name"
        }
      }
    ]`;

    // Generate a cache key based on the activities
    const cacheKey = useCache ? `insights_${generateCacheKey(activityData)}` : undefined;

    // Call the LLM service
    const content = await llmService.generateResponse(
      userPrompt,
      systemPrompt,
      {
        forceProvider,
        cacheKey,
        responseFormat: 'json',
      }
    );

    try {
      const parsedResponse = JSON.parse(content);
      return parsedResponse.insights || [];
    } catch (parseError) {
      console.error('[LLM Service] Failed to parse response:', parseError);
      return [];
    }
  } catch (error) {
    console.error('[LLM Service] Error analyzing activities:', error);
    return [];
  }
}

/**
 * Generate a cache key from the activity data
 */
function generateCacheKey(data: any): string {
  // Create a deterministic string from the data
  const str = JSON.stringify(data, Object.keys(data).sort());

  // Simple hash function
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32bit integer
  }

  return `${hash}`;
}

/**
 * Enhances a single insight with more context and suggestions
 * @param insight Base insight to enhance
 * @param userContext Additional user context
 * @param options Configuration options for the enhancement
 * @returns Enhanced insight with better suggestions
 */
export async function enhanceInsight(
  insight: Insight,
  userContext: Record<string, unknown>,
  options: {
    useCache?: boolean;
    forceProvider?: 'openai' | 'google';
  } = {}
) {
  if (!insight) return null;

  const { useCache = true, forceProvider } = options;

  try {
    // System prompt for insight enhancement
    const systemPrompt = `You are an AI assistant for a legal practice management system.
    Enhance the following insight with more context-aware suggestions based on the user's role and preferences.`;

    // User prompt with insight and context data
    const userPrompt = `Enhance this insight: ${JSON.stringify(insight)}
    User context: ${JSON.stringify(userContext)}

    Return a JSON object with the enhanced insight, including better suggestions and priority.`;

    // Generate a cache key
    const cacheKey = useCache ? `enhance_${insight.id}_${generateCacheKey(userContext)}` : undefined;

    // Call the LLM service
    const content = await llmService.generateResponse(
      userPrompt,
      systemPrompt,
      {
        forceProvider,
        cacheKey,
        responseFormat: 'json',
      }
    );

    try {
      const parsed = JSON.parse(content);
      return parsed.enhancedInsight as Insight || insight;
    } catch (parseError) {
      console.error('[LLM Service] Failed to parse enhanced insight:', parseError);
      return insight;
    }
  } catch (error) {
    console.error('[LLM Service] Error enhancing insight:', error);
    return insight;
  }
}

/**
 * Get LLM service statistics including cache hits/misses
 * @returns Cache statistics
 */
export function getLLMStats() {
  return llmService.getCacheStats();
}

/**
 * Clear the LLM service cache
 */
export function clearLLMCache() {
  llmService.clearCache();
}

/**
 * Update the LLM configuration
 * @param config Configuration updates
 */
export function updateLLMConfig(config: {
  primaryProvider?: 'openai' | 'google';
  primaryModel?: string;
  fallbackProvider?: 'openai' | 'google';
  fallbackModel?: string;
}) {
  const { primaryProvider, primaryModel, fallbackProvider, fallbackModel } = config;

  if (primaryProvider || primaryModel) {
    llmService.updatePrimaryConfig({
      ...(primaryProvider && { provider: primaryProvider }),
      ...(primaryModel && { model: primaryModel }),
    });
  }

  if (fallbackProvider || fallbackModel) {
    llmService.updateFallbackConfig({
      ...(fallbackProvider && { provider: fallbackProvider }),
      ...(fallbackModel && { model: fallbackModel }),
    });
  }
}

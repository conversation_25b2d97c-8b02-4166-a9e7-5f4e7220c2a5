/**
 * User schema definitions
 * Contains types for user authentication and authorization
 */

import { UserRole } from '@/lib/types/auth';

/**
 * Authenticated user information
 * Represents the user data available after authentication
 * Follows the JWT claims structure from the system
 */
export interface AuthUser {
  id: string;        // User's unique identifier (from JWT 'sub' claim)
  email: string;     // User's email address
  role: UserRole;    // User's role in the system
  tenantId: string;  // Tenant identifier (for multi-tenancy)
  // Additional fields that might be useful
  firstName?: string;
  lastName?: string;
  isActive?: boolean;
  lastLogin?: Date | string;
  metadata?: Record<string, unknown>;
}

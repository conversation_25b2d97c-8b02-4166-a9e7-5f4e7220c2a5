'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useSupabase } from '@/lib/supabase/provider'
import { generateDeviceFingerprint } from './auth-security-logger'

interface SecurityContextType {
  isDeviceTrusted: boolean | null
  trustDevice: () => Promise<boolean>
  blockDevice: (fingerprint?: string) => Promise<boolean>
  registerDevice: () => Promise<string>
  deviceFingerprint: string | null
  isLoading: boolean
}

const SecurityContext = createContext<SecurityContextType | undefined>(undefined)

export function SecurityProvider({ children }: { children: React.ReactNode }) {
  const { supabase } = useSupabase()
  const router = useRouter()
  const [deviceFingerprint, setDeviceFingerprint] = useState<string | null>(null)
  const [isDeviceTrusted, setIsDeviceTrusted] = useState<boolean | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Helper to check if a Supabase function exists
  const checkFunctionExists = async (functionName: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/utils/function-exists?name=${functionName}`)
      const { data: exists } = await response.json()
      return !!exists
    } catch (error) {
      console.error(`Error checking if function ${functionName} exists:`, error)
      return false
    }
  }

  // Generate and register device fingerprint on mount
  useEffect(() => {
    const initializeDevice = async () => {
      try {
        // Skip device security checks if functions don&apos;t exist
        const hasRegisterDevice = await checkFunctionExists('register_device')
        if (!hasRegisterDevice) {
          console.log('⚠️ Security functions missing - disabling device security')
          setIsDeviceTrusted(true) // Assume device is trusted to avoid blocking user
          setIsLoading(false)
          return
        }

        const fingerprint = await registerDevice()

        // Check if device is trusted
        const { data, error } = await supabase.schema('security').rpc(
          'is_trusted_device',
          { p_fingerprint: fingerprint }
        )

        if (error) {
          console.error('Error checking device trust status:', error)

          // Try fallback to public schema
          try {
            const { data: publicData, error: publicError } = await supabase.rpc(
              'is_trusted_device',
              { p_fingerprint: fingerprint }
            )

            if (publicError) {
              console.error('Error checking device with public schema:', publicError)
              setIsDeviceTrusted(true) // Default to true to avoid blocking user
            } else {
              setIsDeviceTrusted(publicData)
            }
          } catch (fallbackError) {
            console.error('Fallback trust check failed:', fallbackError)
            setIsDeviceTrusted(true) // Default to true to avoid blocking user
          }
        } else {
          setIsDeviceTrusted(data)
        }

        setIsLoading(false)
      } catch (error) {
        console.error('Error initializing device:', error)
        setIsLoading(false)
      }
    }

    initializeDevice()
  }, [supabase])

  // Set up session timeout checker
  useEffect(() => {
    const checkSessionTimeout = async () => {
      try {
        // Check if the timeout function exists before calling it
        const hasTimeoutFunction = await checkFunctionExists('is_session_timed_out')
        if (!hasTimeoutFunction) {
          // Skip timeout check if function doesn't exist
          console.log('⚠️ Session timeout function missing - skipping check')
          return
        }

        // Try calling the function with fully qualified name
        if (supabase) { // Add null check for supabase
          const { data, error } = await supabase.rpc(
            'is_session_timed_out',
            {}
          )

          if (error) {
            console.error('Error checking session timeout:', error)

            // Try direct SQL query as fallback
            try {
              const { data: sqlData, error: sqlError } = await supabase
                .rpc('is_session_timed_out', {})

              if (sqlError) {
                console.error('SQL fallback error:', sqlError)
                return
              }

              // If session is timed out, redirect to login
              if (sqlData === true) {
                await supabase.auth.signOut()
                router.push('/login?reason=timeout')
              }
            } catch (sqlFallbackError) {
              console.error('SQL fallback failed:', sqlFallbackError)
            }
            return
          }

          // If session is timed out, redirect to login
          if (data === true) {
            await supabase.auth.signOut()
            router.push('/login?reason=timeout')
          }
        }
      } catch (error) {
        console.error('Error checking session timeout:', error)
      }
    }

    // Check every minute
    const interval = setInterval(checkSessionTimeout, 60000)

    // Initial check
    checkSessionTimeout()

    return () => clearInterval(interval)
  }, [supabase, router])

  // Register device
  const registerDevice = async () => {
    try {
      // Generate device fingerprint
      const deviceFp = await generateDeviceFingerprint()
      setDeviceFingerprint(deviceFp)

      // Get device info
      const userAgent = typeof window !== 'undefined' ? window.navigator.userAgent : 'Unknown'
      const deviceName = `${userAgent.split(' ').slice(0, 3).join(' ')}...`

      // Check if register_device function exists
      const hasRegisterFunction = await checkFunctionExists('register_device')
      if (!hasRegisterFunction) {
        console.log('Device registration skipped: function security.register_device does not exist')
        return deviceFp
      }

      // Register the device with security schema
      if (supabase) { // Add null check for supabase
        const { data, error } = await supabase.schema('security').rpc(
          'register_device',
          {
            p_fingerprint: deviceFp,
            p_device_name: deviceName
          }
        )

        if (error) {
          console.error('Error naming device:', error)
          // Try fallback to public schema if security schema fails
          try {
            const { data: publicData, error: publicError } = await supabase.rpc(
              'name_device',
              {
                p_fingerprint: deviceFp,
                p_device_name: deviceName
              }
            )

            if (publicError) {
              console.error('Error naming device with public schema:', publicError)
            }
          } catch (fallbackError) {
            console.error('Fallback registration failed:', fallbackError)
          }
        }

        return deviceFp
      }
      // Return empty string if supabase client is not available
      return ''
    } catch (error) {
      console.error('Error naming device:', error)
      // Generate a fallback fingerprint if needed
      const fallbackFp = await generateDeviceFingerprint()
      return fallbackFp
    }
  }

  // Trust this device
  const trustDevice = async (): Promise<boolean> => {
    try {
      if (!deviceFingerprint) {
        console.error('No device fingerprint available to trust');
        return false;
      }

      // If function doesn't exist, skip
      const hasTrustFunction = await checkFunctionExists('trust_device')
      if (!hasTrustFunction) {
        console.log('Device trust skipped: function security.trust_device does not exist')
        return true;
      }

      // Trust the device with security schema
      if (supabase) {
        const { data, error } = await supabase.schema('security').rpc(
          'trust_device',
          { p_fingerprint: deviceFingerprint }
        )

        if (error) {
          console.error('Error trusting device:', error)
          // Try fallback to public schema
          try {
            const { data: publicData, error: publicError } = await supabase.rpc(
              'trust_device',
              { p_fingerprint: deviceFingerprint }
            )

            if (publicError) {
              console.error('Error trusting device with public schema:', publicError)
              return false
            }
          } catch (fallbackError) {
            console.error('Fallback trust failed:', fallbackError)
            return false
          }
        }

        // Successfully trusted device
        setIsDeviceTrusted(true)
        return true
      }
      // Return false if supabase client is not available
      return false;
    } catch (error) {
      console.error('Error trusting device:', error)
      return false
    }
  }

  // Block a device
  const blockDevice = async (fingerprint?: string): Promise<boolean> => {
    try {
      const fpToBlock = fingerprint || deviceFingerprint

      if (!fpToBlock) {
        console.error('No device fingerprint available to block');
        return false;
      }

      // If function doesn't exist, skip
      const hasBlockFunction = await checkFunctionExists('block_device')
      if (!hasBlockFunction) {
        console.log('Device block skipped: function security.block_device does not exist')
        return true;
      }

      if (supabase) {
        const { data, error } = await supabase.schema('security').rpc(
          'block_device',
          { p_fingerprint: fpToBlock }
        )

        if (error) {
          console.error('Error blocking device:', error)
          // Try fallback to public schema
          try {
            const { data: publicData, error: publicError } = await supabase.rpc(
              'block_device',
              { p_fingerprint: fpToBlock }
            )

            if (publicError) {
              console.error('Error blocking device with public schema:', publicError)
              return false
            }
          } catch (fallbackError) {
            console.error('Fallback block failed:', fallbackError)
            return false
          }
        }

        // If we&apos;re blocking this device, clear the trusted state
        if (fpToBlock === deviceFingerprint) {
          setIsDeviceTrusted(false)
        }

        return true
      }
      // Return false if supabase client is not available
      return false;
    } catch (error) {
      console.error('Error blocking device:', error)
      return false
    }
  }

  return (
    <SecurityContext.Provider
      value={{
        isDeviceTrusted,
        trustDevice,
        blockDevice,
        registerDevice,
        deviceFingerprint,
        isLoading
      }}
    >
      {children}
    </SecurityContext.Provider>
  )
}

export const useSecurity = () => {
  const context = useContext(SecurityContext)
  if (context === undefined) {
    throw new Error('useSecurity must be used within a SecurityProvider')
  }
  return context
}

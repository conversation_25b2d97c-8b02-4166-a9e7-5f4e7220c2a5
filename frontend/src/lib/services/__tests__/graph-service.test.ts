// Import the GraphService class to test, but we'll mock its dependencies
import { GraphService } from '../graph-service';
// Mock the getDriver function instead of importing it
const mockDriver = {
  session: jest.fn(),
  close: jest.fn()
};
import neo4j, { Driver, Session, QueryResult, Record as Neo4jR<PERSON>ord, Integer } from 'neo4j-driver';

// Create mock session
const mockSession = {
  run: jest.fn(),
  close: jest.fn(),
};

// Create mock driver
const mockNeo4jDriver = {
  session: jest.fn(() => mockSession),
  verifyConnectivity: jest.fn().mockResolvedValue({}),
  close: jest.fn(),
};

// Mock the entire neo4j-driver library
jest.mock('neo4j-driver', () => ({
  driver: jest.fn(() => mockNeo4jDriver),
  auth: {
    basic: jest.fn(),
  },
  // @ts-expect-error - simplified implementation for testing
  isInt: (val: any): val is Integer => typeof val === 'number' || typeof val === 'bigint',
  integer: {
    fromNumber: (num: number) => BigInt(num),
    toNumber: (int: Integer | number | bigint) => Number(int),
  },
  isPoint: jest.fn().mockReturnValue(false),
}));

// Rather than mocking the entire module, we'll just make a test version of GraphService
// that doesn't rely on the global getDriver function
// Use a type assertion to make TypeScript happy with our test class
// @ts-expect-error - Intentionally overriding private method for testing
class TestGraphService extends GraphService {
  // Override the private getSession method to return our mock session
  // @ts-expect-error - We're intentionally overriding a private method for testing
  getSession(): Session {
    return mockSession as unknown as Session;
  }
}

describe('GraphService', () => {
  let graphService: GraphService;
  let originalEnv: NodeJS.ProcessEnv;

  beforeAll(() => {
    // Store original environment variables if needed
    originalEnv = { ...process.env };

    // Neo4j connection details are already mocked at the module level
    // so we don't need to set environment variables
  });

  afterAll(() => {
    // Restore original environment variables
    process.env = originalEnv;
  });

  beforeEach(() => {
    // Reset mocks before each test
    mockSession.run.mockClear();
    mockSession.close.mockClear();
    mockNeo4jDriver.session.mockClear();
    mockNeo4jDriver.verifyConnectivity.mockClear();
    mockNeo4jDriver.close.mockClear();

    // Clear any previous mock implementations
    mockSession.run.mockReset();

    // Create a new instance of our test class for each test to ensure isolation
    // @ts-expect-error - We're using a test class that extends GraphService
    graphService = new TestGraphService();
  });

  describe('getPersonNetwork', () => {
    const personId = 'person-123';
    const tenantId = 'tenant-abc';

    it('should return nodes and links when person has connections', async () => {
      // Arrange: Mock the neo4j response for a person with neighbors
      const mockNodes = [
        {
          properties: { person_id: personId, name: 'Alice', tenant_id: tenantId },
          labels: ['Person'],
          identity: { toString: () => '1' } // Add identity property
        },
        {
          properties: { case_id: 'case-001', title: 'Case Alpha', tenant_id: tenantId },
          labels: ['Case'],
          identity: { toString: () => '2' }
        },
        {
          properties: { person_id: 'person-456', name: 'Bob', tenant_id: tenantId },
          labels: ['Person'],
          identity: { toString: () => '3' }
        }
      ];
      const mockRels = [
        {
          properties: { type: 'CLIENT_IN' },
          type: 'CLIENT_IN',
          start: { toString: () => '1' }, // Mock identity for start node
          end: { toString: () => '2' },    // Mock identity for end node
          startNodeElementId: 'id-alice',
          endNodeElementId: 'id-case-alpha'
        },
        {
          properties: { type: 'WORKS_WITH' },
          type: 'WORKS_WITH',
          start: { toString: () => '1' },
          end: { toString: () => '3' },
          startNodeElementId: 'id-alice',
          endNodeElementId: 'id-bob'
        }
      ];

      const mockResult = {
        records: [
          {
            keys: ['p', 'n', 'r'], // Match actual query return keys after apoc call
            get: (key: string) => {
              if (key === 'p') return mockNodes[0]; // Return the main person node
              if (key === 'n') return mockNodes.slice(1); // Return neighbor nodes (Set n)
              if (key === 'r') return mockRels; // Return relationships (Set r)
              return undefined;
            },
            // Add other necessary Record methods/properties if used by the service
            _fields: [mockNodes[0], mockNodes.slice(1), mockRels], // Match structure
            length: 3,
          },
        ],
        summary: { // Add mock summary if needed
          counters: { _stats: { nodesCreated: 0, relationshipsCreated: 0 } },
          // Add other summary properties if accessed
        },
      };

      mockSession.run.mockResolvedValue(mockResult);

      // Act
      const result = await graphService.getPersonNetwork(personId, tenantId);

      // Assert
      expect(mockSession.run).toHaveBeenCalledWith(expect.any(String), {
        personId: 'person-123',
        tenantId: 'tenant-abc',
      });

      // Check the structure of the result
      expect(result.nodes).toBeDefined();
      expect(result.links).toBeDefined();
      expect(result.nodes.length).toBeGreaterThan(0);
      expect(result.links.length).toBeGreaterThan(0);
      // Add more specific checks if needed
    });

    it('should return only the person node when they have no connections', async () => {
      // Arrange: Mock the neo4j response for a person with no neighbors
      const mockNodes = [
        {
          properties: { person_id: personId, name: 'Alice Isolated', tenant_id: tenantId },
          labels: ['Person'],
          identity: { toString: () => '1' } // Add identity property
        }
      ];
      const mockRels: unknown[] = []; // Empty relationships
      const mockNeighborNodes: unknown[] = []; // Empty neighbors

      const mockResult = {
        records: [
          {
            keys: ['p', 'n', 'r'], // Match actual query return keys after apoc call
            get: (key: string) => {
              if (key === 'p') return mockNodes[0]; // Return the single person node
              if (key === 'n') return mockNeighborNodes; // Return empty array
              if (key === 'r') return mockRels; // Return empty array
              return undefined;
            },
            // Add other necessary Record methods/properties if used by the service
            _fields: [mockNodes[0], mockNeighborNodes, mockRels], // Match structure
            length: 3, // Number of fields returned
          },
        ],
        summary: { // Add mock summary if needed
          counters: { _stats: { nodesCreated: 0, relationshipsCreated: 0 } },
          // Add other summary properties if accessed
        },
      };

      mockSession.run.mockResolvedValue(mockResult);

      // Act
      const result = await graphService.getPersonNetwork(personId, tenantId);

      // Assert
      expect(mockSession.run).toHaveBeenCalledTimes(1);
      expect(result.nodes).toBeDefined();
      expect(result.links).toBeDefined();
      expect(result.nodes).toHaveLength(1); // Only the person node should exist
      expect(result.links).toHaveLength(0); // No links should exist
      expect(result.nodes[0].id).toEqual('person-123'); // Correct assertion: The service uses person_id from properties
      expect(result.nodes[0].properties.name).toEqual('Alice Isolated'); // Access name via properties
    });

    it('should return an empty graph when the person is not found', async () => {
      // Arrange: Mock the neo4j response for no matching person
      const mockResult = {
        records: [], // No records found
        summary: { /* mock summary if needed */ }
      };
      // Mock the first run (main query) finding nothing
      mockSession.run.mockResolvedValueOnce(mockResult);

      // Mock the second run (fallback person query) finding nothing either
      mockSession.run.mockResolvedValueOnce({ records: [] });

      // Act
      const result = await graphService.getPersonNetwork(personId, tenantId);

      // Assert
      expect(mockSession.run).toHaveBeenCalledTimes(2); // It's called twice now
      expect(result.nodes).toEqual([]);
      expect(result.links).toEqual([]);
    });

    // Optional: Test error handling
    it('should throw an error if the database connection fails', async () => {
      // Configure the run method to throw an error
      mockSession.run.mockRejectedValue(new Error('Database connection failed'));

      // Act & Assert: Expect the service method to reject with an error
      // @ts-expect-error - Jest types issue with .rejects
      await expect(graphService.getPersonNetwork(personId, tenantId))
          // @ts-expect-error - Jest types issue with .rejects
          .rejects
          .toThrow('Failed to retrieve person network from graph database.');

      // Verify that session close was called (important for resource cleanup)
      expect(mockSession.close).toHaveBeenCalled();
    });

  });

  describe('getStaffCaseLoad', () => {
    const staffId = 'staff-123';
    const tenantId = 'tenant-abc';

    it('should return cases handled by the staff member', async () => {
      // Mock the Neo4j record format for cases
      const mockCaseNodes = [
        {
          properties: {
            case_id: 'case-001',
            tenant_id: tenantId,
            title: 'Johnson Divorce',
            status: 'active',
            description: 'Divorce settlement case',
            client_id: 'client-111',
            created_at: '2025-03-15T00:00:00.000Z',
            updated_at: '2025-03-20T00:00:00.000Z',
            metadata: JSON.stringify({ priority: 'high' }),
            sensitive: true,
            created_by: 'staff-123'
          }
        },
        {
          properties: {
            case_id: 'case-002',
            tenant_id: tenantId,
            title: 'Smith Contract',
            status: 'active',
            description: 'Business contract review',
            client_id: 'client-222',
            created_at: '2025-03-17T00:00:00.000Z',
            updated_at: '2025-03-21T00:00:00.000Z',
            metadata: JSON.stringify({ priority: 'medium' }),
            sensitive: false,
            created_by: 'staff-123'
          }
        }
      ];

      // Create a mock result similar to what Neo4j would return
      const mockResult = {
        records: mockCaseNodes.map(caseNode => ({
          keys: ['c'],
          get: (key: string) => key === 'c' ? caseNode : undefined,
          _fields: [caseNode]
        }))
      };

      // Set up our mock to return this result
      mockSession.run.mockResolvedValue(mockResult);

      // Call the method
      const cases = await graphService.getStaffCaseLoad(staffId, tenantId);

      // Verify results
      expect(cases).toHaveLength(2);
      expect(cases[0].id).toBe('case-001');
      expect(cases[0].title).toBe('Johnson Divorce');
      expect(cases[0].metadata).toEqual({ priority: 'high' });
      expect(cases[1].id).toBe('case-002');
      // @ts-expect-error - The Case type might not include sensitive property
      expect(cases[1].sensitive).toBe(false);

      // Verify the query was called with correct parameters
      expect(mockSession.run).toHaveBeenCalledWith(
        expect.any(String),
        { staffId, tenantId }
      );
      expect(mockSession.close).toHaveBeenCalled();
    });

    it('should return an empty array when staff has no cases', async () => {
      // Mock an empty result
      mockSession.run.mockResolvedValue({ records: [] });

      // Call the method
      const cases = await graphService.getStaffCaseLoad(staffId, tenantId);

      // Verify results
      expect(cases).toEqual([]);
      expect(cases).toHaveLength(0);

      // Verify the query was called with correct parameters
      expect(mockSession.run).toHaveBeenCalledWith(
        expect.any(String),
        { staffId, tenantId }
      );
    });

    it('should handle database errors gracefully', async () => {
      // Mock a database error
      mockSession.run.mockRejectedValue(new Error('Neo4j query failed'));

      // Expect the method to throw an error
      // @ts-expect-error - Jest types issue with .rejects
      await expect(graphService.getStaffCaseLoad(staffId, tenantId))
        // @ts-expect-error - Jest types issue with .rejects
        .rejects
        .toThrow('Failed to retrieve staff caseload from graph database');

      // Verify session was closed even after error
      expect(mockSession.close).toHaveBeenCalled();
    });
  });

  describe('getCaseNetwork', () => {
    const caseId = 'case-001';
    const tenantId = 'tenant-abc';

    it('should return nodes and links for a case network', async () => {
      // Mock nodes and relationships in the case network
      const mockNodes = [
        {
          // The case node
          identity: { toString: () => '1' },
          labels: ['Case'],
          properties: {
            case_id: caseId,
            tenant_id: tenantId,
            title: 'Johnson Divorce'
          }
        },
        {
          // A person node (client)
          identity: { toString: () => '2' },
          labels: ['Person'],
          properties: {
            person_id: 'person-111',
            tenant_id: tenantId,
            name: 'Sarah Johnson'
          }
        },
        {
          // A task node
          identity: { toString: () => '3' },
          labels: ['Task'],
          properties: {
            task_id: 'task-123',
            tenant_id: tenantId,
            title: 'File divorce papers'
          }
        }
      ];

      const mockRels = [
        {
          type: 'CLIENT_IN',
          start: { toString: () => '2' },  // person-111
          end: { toString: () => '1' },    // case-001
          properties: { relationship_start: '2025-01-15' }
        },
        {
          type: 'RELATED_TO',
          start: { toString: () => '3' },  // task-123
          end: { toString: () => '1' },    // case-001
          properties: { created_at: '2025-03-20' }
        }
      ];

      // Create a mock result similar to what Neo4j would return
      const mockResult = {
        records: [{
          keys: ['nodes', 'rels'],
          get: (key: string) => {
            if (key === 'nodes') return mockNodes;
            if (key === 'rels') return mockRels;
            return undefined;
          }
        }]
      };

      // Set up our mock to return this result
      mockSession.run.mockResolvedValue(mockResult);

      // Call the method
      const network = await graphService.getCaseNetwork(caseId, tenantId);

      // Verify results
      expect(network.nodes).toHaveLength(3);
      expect(network.links).toHaveLength(2);

      // Check nodes
      const caseNode = network.nodes.find(n => n.id === caseId);
      expect(caseNode).toBeDefined();
      expect(caseNode?.label).toBe('Case');

      const personNode = network.nodes.find(n => n.id === 'person-111');
      expect(personNode).toBeDefined();
      expect(personNode?.label).toBe('Person');

      // Check links
      const clientLink = network.links.find(l => l.type === 'CLIENT_IN');
      expect(clientLink).toBeDefined();
      expect(clientLink?.source).toBe('person-111');
      expect(clientLink?.target).toBe(caseId);

      // Verify the query was called with correct parameters
      expect(mockSession.run).toHaveBeenCalledWith(
        expect.any(String),
        { caseId, tenantId }
      );
      expect(mockSession.close).toHaveBeenCalled();
    });

    it('should return just the case node when there are no connections', async () => {
      // Mock just the case node with no connections
      const mockCaseNode = {
        identity: { toString: () => '1' },
        labels: ['Case'],
        properties: {
          case_id: caseId,
          tenant_id: tenantId,
          title: 'Isolated Case'
        }
      };

      // First return empty results
      mockSession.run.mockResolvedValueOnce({ records: [] });

      // Then return just the case node on the fallback query
      mockSession.run.mockResolvedValueOnce({
        records: [{
          keys: ['c'],
          get: (key: string) => key === 'c' ? mockCaseNode : undefined
        }]
      });

      // Call the method
      const network = await graphService.getCaseNetwork(caseId, tenantId);

      // Verify results - should have just the case node and no links
      expect(network.nodes).toHaveLength(1);
      expect(network.links).toHaveLength(0);
      expect(network.nodes[0].id).toBe(caseId);
      expect(network.nodes[0].label).toBe('Case');

      // Verify both queries were called
      expect(mockSession.run).toHaveBeenCalledTimes(2);
      expect(mockSession.close).toHaveBeenCalled();
    });

    it('should return empty network for non-existent case', async () => {
      // Mock empty results for both queries
      mockSession.run.mockResolvedValueOnce({ records: [] });
      mockSession.run.mockResolvedValueOnce({ records: [] });

      // Call the method
      const network = await graphService.getCaseNetwork('nonexistent-case', tenantId);

      // Verify results - should be empty
      expect(network.nodes).toHaveLength(0);
      expect(network.links).toHaveLength(0);

      // Verify both queries were called
      expect(mockSession.run).toHaveBeenCalledTimes(2);
      expect(mockSession.close).toHaveBeenCalled();
    });
  });

  describe('runConflictCheck', () => {
    const tenantId = 'tenant-abc';

    it('should identify conflicts with existing clients', async () => {
      const checkInput = {
        personName: 'John Smith',
        companyName: 'Acme Corp',
        associatedCaseId: 'case-001'
      };

      // Mock conflict results
      const mockConflictPerson = {
        properties: {
          person_id: 'person-123',
          name: 'John Smith',
          tenant_id: tenantId
        }
      };

      const mockConflictCase = {
        properties: {
          case_id: 'case-789',
          title: 'Smith vs ABC Company',
          status: 'Active',
          tenant_id: tenantId
        }
      };

      // Create a mock result with one conflict
      const mockResult = {
        records: [{
          keys: ['p', 'c', 'conflictType'],
          get: (key: string) => {
            if (key === 'p') return mockConflictPerson;
            if (key === 'c') return mockConflictCase;
            if (key === 'conflictType') return 'Existing Client Conflict';
            return undefined;
          }
        }]
      };

      // Set up our mock to return this result
      mockSession.run.mockResolvedValue(mockResult);

      // Call the method
      const conflicts = await graphService.runConflictCheck(checkInput, tenantId);

      // Verify results
      expect(conflicts).toHaveLength(1);
      expect(conflicts[0].type).toBe('Existing Client Conflict');
      expect(conflicts[0].conflictingEntity).toEqual(mockConflictPerson.properties);
      expect(conflicts[0].relatedCase).toEqual(mockConflictCase.properties);

      // Verify the query was called with correct parameters
      expect(mockSession.run).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          personName: 'John Smith',
          tenantId,
          associatedCaseId: 'case-001'
        })
      );
      expect(mockSession.close).toHaveBeenCalled();
    });

    it('should return empty array when no conflicts found', async () => {
      const checkInput = {
        personName: 'Unique Name',
        companyName: 'New Company'
      };

      // Mock empty conflict results
      mockSession.run.mockResolvedValue({ records: [] });

      // Call the method
      const conflicts = await graphService.runConflictCheck(checkInput, tenantId);

      // Verify results - should be empty
      expect(conflicts).toEqual([]);
      expect(conflicts).toHaveLength(0);

      // Verify the query was called with correct parameters
      expect(mockSession.run).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          personName: 'Unique Name',
          tenantId,
          associatedCaseId: null  // No case ID provided
        })
      );
    });

    it('should handle database errors gracefully', async () => {
      const checkInput = { personName: 'Test Person' };

      // Mock a database error
      mockSession.run.mockRejectedValue(new Error('Neo4j query failed'));

      // Expect the method to throw an error
      // @ts-expect-error - Jest types issue with .rejects
      await expect(graphService.runConflictCheck(checkInput, tenantId))
        // @ts-expect-error - Jest types issue with .rejects
        .rejects
        .toThrow('Failed to run conflict check in graph database');

      // Verify session was closed even after error
      expect(mockSession.close).toHaveBeenCalled();
    });
  })
});

// src/lib/services/calendar-deadline-service.ts
import type { TypedSupabaseClient } from '@/lib/supabase/client-types';
import { z } from 'zod';
import { createUserService, UserService } from './user-service';

// Zod schema for deadline validation
export const DeadlineSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  case_id: z.string().uuid(),
  due_date: z.string().datetime({ message: 'Valid due date is required' }),
  rule_id: z.string().uuid().optional(),
  base_date: z.string().datetime().optional(),
  calculation_notes: z.string().optional(),
  assigned_to: z.array(z.string().uuid()).optional(),
  priority: z.enum(['high', 'medium', 'low']).optional().default('medium'),
  status: z.enum(['pending', 'completed', 'overdue']).optional().default('pending'),
  metadata: z.record(z.any()).optional(),
});

export type CalendarDeadline = {
  id: string;
  tenant_id: string;
  case_id: string;
  title: string;
  description?: string | null;
  due_date: string;
  rule_id?: string | null;
  base_date?: string | null;
  calculation_notes?: string | null;
  created_by: string;
  assigned_to?: string[] | null;
  priority?: string | null;
  status?: string | null;
  completed_at?: string | null;
  completed_by?: string | null;
  metadata?: any;
  created_at: string;
  updated_at?: string | null;
};

export type DeadlineService = CalendarDeadlineService;

export interface CalendarDeadlineWithRelations extends CalendarDeadline {
  creator?: any;
  assignees?: unknown[];
  case_info?: any;
  rule_info?: any;
  completer?: any;

  // Adding properties that are being referenced in the application
  validation_status?: string;
  jurisdiction?: string;
  legal_basis?: string;
  document_id?: string;
  document?: any;
}

/**
 * Calendar Deadline Service
 * Responsible for all calendar deadline-related data operations
 */
export class CalendarDeadlineService {
  private supabase: TypedSupabaseClient;
  private tenantId: string;
  private userService: UserService;

  constructor(supabase: TypedSupabaseClient, tenantId: string) {
    this.supabase = supabase;
    this.tenantId = tenantId;
    this.userService = createUserService(supabase, tenantId);
  }

  /**
   * Get all computed deadlines for the current tenant from task_deadlines table.
   */
  async getAll(options: {
    start_date?: string;
    end_date?: string;
    case_id?: string; // Note: Filtering by case_id requires joining with tasks or adding case_id to task_deadlines
    // status?: string; // Status might be derived from task or managed on task_deadline? PRD doesn't specify status on task_deadlines
    // priority?: string; // Priority is likely on the task, not the computed deadline
    page?: number;
    limit?: number;
  } = {}): Promise<{ deadlines: Partial<CalendarDeadlineWithRelations>[]; totalCount: number }> { // Return type simplified
    try {
      console.log('Fetching computed deadlines from task_deadlines with options:', options);

      const {
        start_date,
        end_date,
        case_id, // We'll ignore this for now unless we add a join
        page = 1,
        limit = 50 // Restore default limit
      } = options;

      // Start building the query against the 'task_deadlines' table
      let query = this.supabase
        .schema('tenants')
        .from('task_deadlines') // *** CORRECTED table name ***
        // Select fields relevant to the calendar view from task_deadlines
        .select('id, task_id, deadline_date, rule_citation, overridden', { count: 'exact' })
        .eq('tenant_id', this.tenantId);

      // Apply date filters
      if (start_date) {
        query = query.gte('deadline_date', start_date);
      }
      if (end_date) {
        // Make end_date inclusive by querying up to the end of that day if it's just a date
        const endMoment = new Date(end_date);
         if (!isNaN(endMoment.getTime()) && end_date.length === 10) { // Check if it's just yyyy-mm-dd
             endMoment.setUTCHours(23, 59, 59, 999);
             query = query.lte('deadline_date', endMoment.toISOString().substring(0, 10)); // Use YYYY-MM-DD format for DATE type column
         } else {
            // If end_date has time, use it directly (though deadline_date is DATE)
             query = query.lte('deadline_date', end_date);
         }
      }

      // TODO: Implement case_id filtering if needed (requires join or schema change)
      if (case_id) {
         console.warn('Filtering by case_id on task_deadlines is not implemented yet.');
         // Example join (untested, assumes tasks has case_id):
         // query = query.select('*, tasks!inner(case_id)')
         //              .eq('tasks.case_id', case_id);
      }


      // Add pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to).order('deadline_date', { ascending: true });

      // Execute the query
      const { data: fetchedDeadlines, error, count } = await query;

      if (error) {
        console.error('Error fetching computed task deadlines:', {
          message: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint,
          fullError: error // Log the full error object too
        });
        throw error;
      }

      console.log(`Fetched ${fetchedDeadlines?.length ?? 0} computed deadlines, total count: ${count}`);

      // Map to a structure compatible with CalendarDeadlineWithRelations (partially)
      // We don't fetch relations here, keep it simple for the calendar view
      const deadlines = fetchedDeadlines?.map(dl => ({
          id: dl.id,
          task_id: dl.task_id, // Include task_id if needed frontend
          deadline_date: dl.deadline_date,
          // Need title? Fetch from associated task or add to task_deadlines?
          // For now, maybe use rule_citation or a placeholder title
          title: dl.rule_citation || `Deadline for Task ${dl.task_id.substring(0, 8)}`, // Placeholder title
          rule_citation: dl.rule_citation,
          overridden: dl.overridden,
          // Add other minimal fields if required by the frontend component
          // Example: status (if added to task_deadlines or fetched from task)
      })) || [];


      return { deadlines: deadlines, totalCount: count ?? 0 };
    } catch (error) {
      console.error('Exception in CalendarDeadlineService.getAll:', error);
      throw error;
    }
  }

  /**
   * Get a calendar deadline by ID with all related data
   */
  async getById(deadlineId: string): Promise<CalendarDeadlineWithRelations | null> {
    try {
      const { data: deadline, error } = await this.supabase
        .schema('tenants')
        .from('deadlines')
        .select('*')
        .eq('id', deadlineId)
        .eq('tenant_id', this.tenantId)
        .single();

      if (error) {
        console.error(`Error fetching calendar deadline by ID ${deadlineId}:`, error);
        return null;
      }

      // Fetch related user data
      const userIds = [deadline.created_by];
      if (deadline.completed_by) userIds.push(deadline.completed_by);
      if (deadline.assigned_to && Array.isArray(deadline.assigned_to)) {
        userIds.push(...deadline.assigned_to);
      }

      const users = await this.userService.getByIds(userIds.filter(Boolean));
      const userMap = new Map(users.map(user => [user.id, user]));

      // Fetch related case data
      let caseInfo = null;
      if (deadline.case_id) {
        const { data: caseData, error: caseError } = await this.supabase
          .schema('tenants')
          .from('cases')
          .select('id, title, status')
          .eq('id', deadline.case_id)
          .single();

        if (!caseError && caseData) {
          caseInfo = caseData;
        }
      }

      // Fetch rule data if needed
      let ruleInfo = null;
      if (deadline.rule_id) {
        const { data: ruleData, error: ruleError } = await this.supabase
          .schema('tenants')
          .from('legal_rules')
          .select('id, name, description, jurisdiction_id')
          .eq('id', deadline.rule_id)
          .single();

        if (!ruleError && ruleData) {
          ruleInfo = ruleData;
        }
      }

      // Helper function to safely convert dates and handle null/undefined values
      const formatDate = (dateStr: string | null | undefined): string | undefined => {
        if (!dateStr) return undefined;
        try {
          return new Date(dateStr).toISOString();
        } catch (e) {
          console.error(`Error parsing date: ${dateStr}`, e);
          return undefined;
        }
      };

      // Build the full deadline object with relations
      const deadlineWithRelations: CalendarDeadlineWithRelations = {
        ...deadline,
        created_at: formatDate(deadline.created_at) || new Date().toISOString(), // Ensure we always have a created_at date
        updated_at: formatDate(deadline.updated_at),
        due_date: formatDate(deadline.due_date) || new Date().toISOString(), // Ensure we always have a due_date
        base_date: formatDate(deadline.base_date),
        completed_at: formatDate(deadline.completed_at),
      };

      // Attach creator information
      if (deadline.created_by && userMap.has(deadline.created_by)) {
        deadlineWithRelations.creator = userMap.get(deadline.created_by);
      }

      // Attach assignees information
      if (deadline.assigned_to && Array.isArray(deadline.assigned_to)) {
        deadlineWithRelations.assignees = deadline.assigned_to
          .map((id: string) => userMap.get(id))
          .filter(Boolean);
      }

      // Attach completer information
      if (deadline.completed_by && userMap.has(deadline.completed_by)) {
        deadlineWithRelations.completer = userMap.get(deadline.completed_by);
      }

      // Attach case information
      if (caseInfo) {
        deadlineWithRelations.case_info = caseInfo;
      }

      // Attach rule information
      if (ruleInfo) {
        deadlineWithRelations.rule_info = ruleInfo;
      }

      return deadlineWithRelations;
    } catch (error) {
      console.error('Exception in CalendarDeadlineService.getById:', error);
      throw error;
    }
  }

  /**
   * Create a new calendar deadline
   */
  async create(userId: string, deadlineData: z.infer<typeof DeadlineSchema>): Promise<CalendarDeadlineWithRelations | null> {
    try {
       // Prepare data for insertion
       const now = new Date().toISOString();
       const deadlineToInsert = {
         title: deadlineData.title,
         description: deadlineData.description,
         case_id: deadlineData.case_id, // Explicitly provide required case_id
         due_date: deadlineData.due_date,
         rule_id: deadlineData.rule_id,
         base_date: deadlineData.base_date,
         calculation_notes: deadlineData.calculation_notes,
         assigned_to: deadlineData.assigned_to,
         priority: deadlineData.priority,
         status: deadlineData.status,
         metadata: deadlineData.metadata,
         tenant_id: this.tenantId,
         created_by: userId,
         created_at: now,
         updated_at: now,
       };

      // Insert the deadline
      const { data: newDeadline, error } = await this.supabase
        .schema('tenants')
        .from('deadlines')
        .insert(deadlineToInsert)
        .select('*')
        .single();

      if (error) {
        console.error('Error creating calendar deadline:', error);
        throw error;
      }

      // Return the created deadline with relations
      return this.getById(newDeadline.id);
    } catch (error) {
      console.error('Exception in CalendarDeadlineService.create:', error);
      throw error;
    }
  }

  /**
   * Update an existing calendar deadline
   */
  async update(deadlineId: string, userId: string, updateData: Partial<z.infer<typeof DeadlineSchema>>): Promise<CalendarDeadlineWithRelations | null> {
    try {
      // First, fetch the existing deadline to check if it exists
      const { data: existingDeadline, error: fetchError } = await this.supabase
        .schema('tenants')
        .from('deadlines')
        .select('*')
        .eq('id', deadlineId)
        .eq('tenant_id', this.tenantId)
        .single();

      if (fetchError || !existingDeadline) {
        console.error('Error fetching deadline for update:', fetchError);
        throw new Error('Calendar deadline not found');
      }

      // Validate the update data using Zod
      const validationResult = DeadlineSchema.partial().safeParse(updateData);
      if (!validationResult.success) {
        console.error('Validation error in calendar deadline update:', validationResult.error);
        throw validationResult.error;
      }

      const validatedData = validationResult.data;

      // Prepare data for update
      const updatePayload: Record<string, unknown> = {
        ...validatedData,
        updated_at: new Date().toISOString(),
      };

      // If status is set to 'completed', add completion fields
      if (validatedData.status === 'completed' && !existingDeadline.completed_at) {
        updatePayload.completed_at = new Date().toISOString();
        updatePayload.completed_by = userId;
      }
      // If status is being changed away from 'completed', clear completion fields
      else if (validatedData.status && validatedData.status !== 'completed' && existingDeadline.completed_at) {
        updatePayload.completed_at = null;
        updatePayload.completed_by = null;
      }

      // Update the deadline
      const { data: updatedDeadline, error } = await this.supabase
        .schema('tenants')
        .from('deadlines')
        .update(updatePayload)
        .eq('id', deadlineId)
        .eq('tenant_id', this.tenantId)
        .select('*')
        .single();

      if (error) {
        console.error('Error updating calendar deadline:', error);
        throw error;
      }

      // Return the updated deadline with relations
      return this.getById(updatedDeadline.id);
    } catch (error) {
      console.error('Exception in CalendarDeadlineService.update:', error);
      throw error;
    }
  }

  /**
   * Delete a calendar deadline
   */
  async delete(deadlineId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .schema('tenants')
        .from('deadlines')
        .delete()
        .eq('id', deadlineId)
        .eq('tenant_id', this.tenantId);

      if (error) {
        console.error('Error deleting calendar deadline:', error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Exception in CalendarDeadlineService.delete:', error);
      throw error;
    }
  }

  /**
   * Mark a deadline as completed
   */
  async markAsCompleted(deadlineId: string, userId: string): Promise<CalendarDeadlineWithRelations | null> {
    try {
      // Prepare update payload
      const updatePayload = {
        status: 'completed',
        completed_at: new Date().toISOString(),
        completed_by: userId,
        updated_at: new Date().toISOString(),
      };

      // Update the deadline
      const { data: updatedDeadline, error } = await this.supabase
        .schema('tenants')
        .from('deadlines')
        .update(updatePayload)
        .eq('id', deadlineId)
        .eq('tenant_id', this.tenantId)
        .select('*')
        .single();

      if (error) {
        console.error('Error marking deadline as completed:', error);
        throw error;
      }

      // Return the updated deadline with relations
      return this.getById(updatedDeadline.id);
    } catch (error) {
      console.error('Exception in CalendarDeadlineService.markAsCompleted:', error);
      throw error;
    }
  }

  /**
   * Update the validation status of a deadline
   */
  async updateValidationStatus(
    deadlineId: string,
    userId: string,
    action: 'validate' | 'reject',
    note?: string
  ): Promise<CalendarDeadlineWithRelations | null> {
    try {
      // Prepare the update payload based on action
      const updatePayload = {
        validation_status: action === 'validate' ? 'validated' : 'rejected',
        validation_date: new Date().toISOString(),
        validated_by: userId,
        validation_note: note || null,
        updated_at: new Date().toISOString()
      };

      // Update the deadline
      const { data: updatedDeadline, error } = await this.supabase
        .schema('tenants')
        .from('deadlines')
        .update(updatePayload)
        .eq('id', deadlineId)
        .eq('tenant_id', this.tenantId)
        .select('*')
        .single();

      if (error) {
        console.error('Error updating deadline validation status:', error);
        throw error;
      }

      // Return the updated deadline with relations
      return this.getById(updatedDeadline.id);
    } catch (error) {
      console.error('Exception in CalendarDeadlineService.updateValidationStatus:', error);
      throw error;
    }
  }
}

// Factory function to create a CalendarDeadlineService instance
export function createCalendarDeadlineService(supabase: TypedSupabaseClient, tenantId: string): CalendarDeadlineService {
  return new CalendarDeadlineService(supabase, tenantId);
}

// src/lib/services/calendar-event-service.ts
import type { SupabaseClient } from '@supabase/supabase-js';
import { z } from 'zod';
import { createUserService, UserService } from './user-service';

// Zod schema for event validation
export const EventSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  start_time: z.string().datetime({ message: 'Valid start time is required' }),
  end_time: z.string().datetime({ message: 'Valid end time is required' }),
  all_day: z.boolean().optional().default(false),
  location: z.string().optional(),
  event_type: z.string().min(1, 'Event type is required'),
  case_id: z.string().uuid().optional(),
  client_id: z.string().uuid().optional(),
  assigned_to: z.array(z.string().uuid()).optional(),
  notification_sent: z.boolean().optional().default(false),
  reminder_time: z.string().optional(), // interval stored as string
  jurisdiction_id: z.string().uuid().optional(),
  status: z.string().optional().default('scheduled'),
  metadata: z.record(z.any()).optional(),
});

export type CalendarEvent = {
  id: string;
  tenant_id: string;
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  all_day?: boolean;
  location?: string;
  event_type: string;
  case_id?: string;
  client_id?: string;
  created_by: string;
  assigned_to?: string[];
  notification_sent?: boolean;
  reminder_time?: string;
  jurisdiction_id?: string;
  status?: string;
  metadata?: any;
  created_at: string;
  updated_at?: string;
};

export interface CalendarEventWithRelations extends CalendarEvent {
  creator?: any;
  assignees?: unknown[];
  case_info?: any;
  client_info?: any;
  jurisdiction_info?: any;
}

/**
 * Calendar Event Service
 * Responsible for all calendar event-related data operations
 */
export class CalendarEventService {
  private supabase: SupabaseClient;
  private tenantId: string;
  private userService: UserService;

  constructor(supabase: SupabaseClient, tenantId: string) {
    this.supabase = supabase;
    this.tenantId = tenantId;
    this.userService = createUserService(supabase, tenantId);
  }

  /**
   * Get all events for the current tenant with related data
   */
  async getAll(options: {
    start_date?: string;
    end_date?: string;
    case_id?: string;
    client_id?: string;
    event_type?: string;
    page?: number;
    limit?: number;
  } = {}): Promise<{ events: CalendarEventWithRelations[]; totalCount: number }> {
    try {
      const {
        start_date,
        end_date,
        case_id,
        client_id,
        event_type,
        page = 1,
        limit = 50
      } = options;

      // Start building the query
      let query = this.supabase
        .schema('tenants')
        .from('events')
        .select('*', { count: 'exact' })
        .eq('tenant_id', this.tenantId);

      // Apply filters if provided
      if (start_date) {
        query = query.gte('start_time', start_date);
      }
      if (end_date) {
        query = query.lte('end_time', end_date);
      }
      if (case_id) {
        query = query.eq('case_id', case_id);
      }
      if (client_id) {
        query = query.eq('client_id', client_id);
      }
      if (event_type) {
        query = query.eq('event_type', event_type);
      }

      // Add pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to).order('start_time', { ascending: true });

      // Execute the query
      const { data: events, error, count } = await query;

      if (error) {
        console.error('Error fetching calendar events:', error);
        throw error;
      }

      // Get unique IDs of all related entities
      const userIds = new Set<string>();
      const caseIds = new Set<string>();
      const clientIds = new Set<string>();
      const jurisdictionIds = new Set<string>();

      events.forEach(event => {
        if (event.created_by) userIds.add(event.created_by);
        if (event.assigned_to && Array.isArray(event.assigned_to)) {
          event.assigned_to.forEach((id: string) => userIds.add(id));
        }
        if (event.case_id) caseIds.add(event.case_id);
        if (event.client_id) clientIds.add(event.client_id);
        if (event.jurisdiction_id) jurisdictionIds.add(event.jurisdiction_id);
      });

      // Fetch all users in bulk
      const users = await this.userService.getByIds(Array.from(userIds));
      const userMap = new Map(users.map(user => [user.id, user]));

      // Fetch all cases in bulk if needed
      let casesMap = new Map();
      if (caseIds.size > 0) {
        const { data: cases, error: casesError } = await this.supabase
          .schema('tenants')
          .from('cases')
          .select('id, title, status')
          .in('id', Array.from(caseIds));

        if (!casesError && cases) {
          casesMap = new Map(cases.map(caseItem => [caseItem.id, caseItem]));
        }
      }

      // Fetch all clients in bulk if needed
      let clientsMap = new Map();
      if (clientIds.size > 0) {
        const { data: clients, error: clientsError } = await this.supabase
          .schema('tenants')
          .from('clients')
          .select('id, first_name, last_name, email, phone')
          .in('id', Array.from(clientIds));

        if (!clientsError && clients) {
          clientsMap = new Map(clients.map(client => [client.id, client]));
        }
      }

      // Fetch all jurisdictions in bulk if needed
      let jurisdictionsMap = new Map();
      if (jurisdictionIds.size > 0) {
        const { data: jurisdictions, error: jurisdictionsError } = await this.supabase
          .schema('tenants')
          .from('jurisdictions')
          .select('id, name, state, country')
          .in('id', Array.from(jurisdictionIds));

        if (!jurisdictionsError && jurisdictions) {
          jurisdictionsMap = new Map(jurisdictions.map(j => [j.id, j]));
        }
      }

      // Map the related data to each event
      const eventsWithRelations = events.map(event => {
        const eventWithRelations: CalendarEventWithRelations = {
          ...event,
          created_at: new Date(event.created_at).toISOString(),
          updated_at: event.updated_at ? new Date(event.updated_at).toISOString() : undefined,
          start_time: new Date(event.start_time).toISOString(),
          end_time: new Date(event.end_time).toISOString(),
        };

        // Attach creator information
        if (event.created_by && userMap.has(event.created_by)) {
          eventWithRelations.creator = userMap.get(event.created_by);
        }

        // Attach assignees information
        if (event.assigned_to && Array.isArray(event.assigned_to)) {
          eventWithRelations.assignees = event.assigned_to
            .map((id: string) => userMap.get(id))
            .filter(Boolean);
        }

        // Attach case information
        if (event.case_id && casesMap.has(event.case_id)) {
          eventWithRelations.case_info = casesMap.get(event.case_id);
        }

        // Attach client information
        if (event.client_id && clientsMap.has(event.client_id)) {
          eventWithRelations.client_info = clientsMap.get(event.client_id);
        }

        // Attach jurisdiction information
        if (event.jurisdiction_id && jurisdictionsMap.has(event.jurisdiction_id)) {
          eventWithRelations.jurisdiction_info = jurisdictionsMap.get(event.jurisdiction_id);
        }

        return eventWithRelations;
      });

      return {
        events: eventsWithRelations,
        totalCount: count || 0
      };
    } catch (error) {
      console.error('Exception in CalendarEventService.getAll:', error);
      throw error;
    }
  }

  /**
   * Get a calendar event by ID with all related data
   */
  async getById(id: string): Promise<CalendarEventWithRelations | null> {
    try {
      const { data: event, error } = await this.supabase
        .schema('tenants')
        .from('events')
        .select('*')
        .eq('id', id)
        .eq('tenant_id', this.tenantId)
        .single();

      if (error) {
        console.error(`Error fetching calendar event by ID ${id}:`, error);
        return null;
      }

      // Fetch related user data
      const userIds = [event.created_by];
      if (event.assigned_to && Array.isArray(event.assigned_to)) {
        userIds.push(...event.assigned_to);
      }

      const users = await this.userService.getByIds(userIds.filter(Boolean));
      const userMap = new Map(users.map(user => [user.id, user]));

      // Fetch related case data if needed
      let caseInfo = null;
      if (event.case_id) {
        const { data: caseData, error: caseError } = await this.supabase
          .schema('tenants')
          .from('cases')
          .select('id, title, status')
          .eq('id', event.case_id)
          .single();

        if (!caseError && caseData) {
          caseInfo = caseData;
        }
      }

      // Fetch related client data if needed
      let clientInfo = null;
      if (event.client_id) {
        const { data: clientData, error: clientError } = await this.supabase
          .schema('tenants')
          .from('clients')
          .select('id, first_name, last_name, email, phone')
          .eq('id', event.client_id)
          .single();

        if (!clientError && clientData) {
          clientInfo = clientData;
        }
      }

      // Fetch jurisdiction data if needed
      let jurisdictionInfo = null;
      if (event.jurisdiction_id) {
        const { data: jurisdictionData, error: jurisdictionError } = await this.supabase
          .schema('tenants')
          .from('jurisdictions')
          .select('id, name, state, country')
          .eq('id', event.jurisdiction_id)
          .single();

        if (!jurisdictionError && jurisdictionData) {
          jurisdictionInfo = jurisdictionData;
        }
      }

      // Build the full event object with relations
      const eventWithRelations: CalendarEventWithRelations = {
        ...event,
        created_at: new Date(event.created_at).toISOString(),
        updated_at: event.updated_at ? new Date(event.updated_at).toISOString() : undefined,
        start_time: new Date(event.start_time).toISOString(),
        end_time: new Date(event.end_time).toISOString(),
      };

      // Attach creator information
      if (event.created_by && userMap.has(event.created_by)) {
        eventWithRelations.creator = userMap.get(event.created_by);
      }

      // Attach assignees information
      if (event.assigned_to && Array.isArray(event.assigned_to)) {
        eventWithRelations.assignees = event.assigned_to
          .map((id: string) => userMap.get(id))
          .filter(Boolean);
      }

      // Attach case information
      if (caseInfo) {
        eventWithRelations.case_info = caseInfo;
      }

      // Attach client information
      if (clientInfo) {
        eventWithRelations.client_info = clientInfo;
      }

      // Attach jurisdiction information
      if (jurisdictionInfo) {
        eventWithRelations.jurisdiction_info = jurisdictionInfo;
      }

      return eventWithRelations;
    } catch (error) {
      console.error('Exception in CalendarEventService.getById:', error);
      throw error;
    }
  }

  /**
   * Create a new calendar event
   */
  async create(userId: string, eventData: z.infer<typeof EventSchema>): Promise<CalendarEventWithRelations | null> {
    try {
      // Validate the input data using Zod
      const validationResult = EventSchema.safeParse(eventData);
      if (!validationResult.success) {
        console.error('Validation error in calendar event creation:', validationResult.error);
        throw validationResult.error;
      }

      const validatedData = validationResult.data;

      // Prepare data for insertion
      const now = new Date().toISOString();
      const { metadata, ...restOfValidatedData } = validatedData; // Separate metadata

      const eventToInsert = {
        ...restOfValidatedData,    // Spread the rest of the fields
        event_data: metadata,      // Map metadata from schema to event_data column
        tenant_id: this.tenantId,
        created_by: userId,
        created_at: now,
        updated_at: now,
      };

      // Insert the event
      const { data: newEvent, error } = await this.supabase
        .schema('tenants')
        .from('events')
        .insert(eventToInsert)
        .select('*')
        .single();

      if (error) {
        console.error('Error creating calendar event:', error);
        throw error;
      }

      // Return the created event with relations
      return this.getById(newEvent.id);
    } catch (error) {
      console.error('Exception in CalendarEventService.create:', error);
      throw error;
    }
  }

  /**
   * Update an existing calendar event
   */
  async update(id: string, userId: string, updateData: Partial<z.infer<typeof EventSchema>>): Promise<CalendarEventWithRelations | null> {
    try {
      // First, fetch the existing event to check if it exists
      const { data: existingEvent, error: fetchError } = await this.supabase
        .schema('tenants')
        .from('events')
        .select('*')
        .eq('id', id)
        .eq('tenant_id', this.tenantId)
        .single();

      if (fetchError || !existingEvent) {
        console.error('Error fetching event for update:', fetchError);
        throw new Error('Calendar event not found');
      }

      // Validate the update data using Zod
      const validationResult = EventSchema.partial().safeParse(updateData);
      if (!validationResult.success) {
        console.error('Validation error in calendar event update:', validationResult.error);
        throw validationResult.error;
      }

      const validatedData = validationResult.data;

      // Prepare data for update
      const updatePayload = {
        ...validatedData,
        updated_at: new Date().toISOString(),
      };

      // Update the event
      const { data: updatedEvent, error } = await this.supabase
        .schema('tenants')
        .from('events')
        .update(updatePayload)
        .eq('id', id)
        .eq('tenant_id', this.tenantId)
        .select('*')
        .single();

      if (error) {
        console.error('Error updating calendar event:', error);
        throw error;
      }

      // Return the updated event with relations
      return this.getById(updatedEvent.id);
    } catch (error) {
      console.error('Exception in CalendarEventService.update:', error);
      throw error;
    }
  }

  /**
   * Delete a calendar event
   */
  async delete(id: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .schema('tenants')
        .from('events')
        .delete()
        .eq('id', id)
        .eq('tenant_id', this.tenantId);

      if (error) {
        console.error('Error deleting calendar event:', error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Exception in CalendarEventService.delete:', error);
      throw error;
    }
  }
}

// Factory function to create a CalendarEventService instance
export function createCalendarEventService(supabase: SupabaseClient, tenantId: string): CalendarEventService {
  return new CalendarEventService(supabase, tenantId);
}

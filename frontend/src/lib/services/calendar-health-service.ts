/**
 * Calendar Health Service
 * 
 * Service for fetching and processing calendar health metrics from Prometheus
 * and other data sources for the superadmin dashboard.
 */

export interface TokenHealthMetrics {
  active: number;
  expired: number;
  expiring_soon: number;
  invalid: number;
}

export interface ProviderHealth {
  provider: string;
  status: 'healthy' | 'degraded' | 'error';
  active_tokens: number;
  uptime: number;
  avg_response_time: number;
  success_rate: number;
  last_error?: string;
  total_requests: number;
  error_requests: number;
}

export interface TenantHealth {
  tenant_id: string;
  tenant_name: string;
  health_score: number;
  providers_connected: number;
  total_providers: number;
  last_sync: string;
  issues: string[];
}

export interface ErrorTrend {
  timestamp: string;
  google_errors: number;
  microsoft_errors: number;
  calendly_errors: number;
}

export interface ExpirationData {
  date: string;
  expiring_count: number;
}

export interface CalendarHealthData {
  tokenHealth: TokenHealthMetrics;
  providerHealth: ProviderHealth[];
  tenantHealth: TenantHealth[];
  errorTrends: ErrorTrend[];
  expirationTimeline: ExpirationData[];
}

export class CalendarHealthService {
  private static instance: CalendarHealthService;
  
  public static getInstance(): CalendarHealthService {
    if (!CalendarHealthService.instance) {
      CalendarHealthService.instance = new CalendarHealthService();
    }
    return CalendarHealthService.instance;
  }

  /**
   * Fetch all calendar health data
   */
  async fetchCalendarHealth(): Promise<CalendarHealthData> {
    try {
      // Fetch metrics from the backend /metrics endpoint
      const response = await fetch('/metrics');
      if (!response.ok) {
        throw new Error(`Failed to fetch metrics: ${response.status}`);
      }
      
      const metricsText = await response.text();
      const parsedMetrics = this.parsePrometheusMetrics(metricsText);
      
      // Process different types of health data
      const tokenHealth = this.processTokenHealth(parsedMetrics);
      const providerHealth = this.processProviderHealth(parsedMetrics);
      const errorTrends = this.generateErrorTrends(parsedMetrics);
      const expirationTimeline = this.generateExpirationTimeline();
      
      // Fetch tenant health from database
      const tenantHealth = await this.fetchTenantHealth();
      
      return {
        tokenHealth,
        providerHealth,
        tenantHealth,
        errorTrends,
        expirationTimeline
      };
    } catch (error) {
      console.error('Error fetching calendar health:', error);
      throw error;
    }
  }

  /**
   * Parse Prometheus metrics text format
   */
  private parsePrometheusMetrics(metricsText: string): Record<string, unknown> {
    const lines = metricsText.split('\n');
    const metrics: Record<string, unknown> = {
      requests: [],
      response_time: []
    };
    
    lines.forEach(line => {
      if (line.startsWith('calendar_api_requests_total')) {
        const match = line.match(/calendar_api_requests_total\{([^}]+)\}\s+(\d+(?:\.\d+)?)/);
        if (match) {
          const labels = this.parseLabels(match[1]);
          const value = parseFloat(match[2]);
          (metrics.requests as any[]).push({ ...labels, value });
        }
      } else if (line.startsWith('calendar_api_response_time')) {
        const match = line.match(/calendar_api_response_time_(\w+)\{([^}]+)\}\s+(\d+(?:\.\d+)?)/);
        if (match) {
          const metricType = match[1];
          const labels = this.parseLabels(match[2]);
          const value = parseFloat(match[3]);
          (metrics.response_time as any[]).push({ type: metricType, ...labels, value });
        }
      }
    });
    
    return metrics;
  }

  /**
   * Parse Prometheus label string
   */
  private parseLabels(labelString: string): Record<string, string> {
    const labels: Record<string, string> = {};
    const matches = labelString.match(/(\w+)="([^"]+)"/g);
    
    if (matches) {
      matches.forEach(match => {
        const [key, value] = match.split('=');
        labels[key] = value.replace(/"/g, '');
      });
    }
    
    return labels;
  }

  /**
   * Process token health metrics
   */
  private processTokenHealth(metrics: any): TokenHealthMetrics {
    // For now, return mock data since token metrics aren't implemented yet
    // In a real implementation, this would parse actual token metrics from Prometheus
    return {
      active: 847,
      expired: 12,
      expiring_soon: 23,
      invalid: 3
    };
  }

  /**
   * Process provider health metrics
   */
  private processProviderHealth(metrics: any): ProviderHealth[] {
    const providers = ['google', 'microsoft', 'calendly'];
    const healthData: ProviderHealth[] = [];
    
    providers.forEach(provider => {
      // Filter requests for this provider
      const requests = metrics.requests?.filter((r: any) => r.provider === provider) || [];
      const successRequests = requests.filter((r: any) => r.status === 'success');
      const errorRequests = requests.filter((r: any) => r.status === 'error');
      
      // Calculate totals
      const totalRequests = requests.reduce((sum: number, r: any) => sum + r.value, 0);
      const successCount = successRequests.reduce((sum: number, r: any) => sum + r.value, 0);
      const errorCount = errorRequests.reduce((sum: number, r: any) => sum + r.value, 0);
      
      // Calculate success rate
      const successRate = totalRequests > 0 ? (successCount / totalRequests) * 100 : 100;
      
      // Calculate average response time from histogram data
      const responseTimes = metrics.response_time?.filter((rt: any) => 
        rt.provider === provider && rt.type === 'sum'
      ) || [];
      const responseCounts = metrics.response_time?.filter((rt: any) => 
        rt.provider === provider && rt.type === 'count'
      ) || [];
      
      let avgResponseTime = 0;
      if (responseTimes.length > 0 && responseCounts.length > 0) {
        const totalTime = responseTimes.reduce((sum: number, rt: any) => sum + rt.value, 0);
        const totalCount = responseCounts.reduce((sum: number, rt: any) => sum + rt.value, 0);
        avgResponseTime = totalCount > 0 ? (totalTime / totalCount) * 1000 : 0; // Convert to ms
      } else {
        // Fallback to mock data
        avgResponseTime = provider === 'google' ? 245 : provider === 'microsoft' ? 1200 : 180;
      }
      
      // Determine status based on success rate and response time
      let status: 'healthy' | 'degraded' | 'error' = 'healthy';
      if (successRate < 95 || avgResponseTime > 2000) status = 'degraded';
      if (successRate < 90 || avgResponseTime > 5000) status = 'error';
      
      // Mock active tokens (in real implementation, get from database)
      const activeTokens = provider === 'google' ? 423 : provider === 'microsoft' ? 312 : 156;
      
      healthData.push({
        provider: provider.charAt(0).toUpperCase() + provider.slice(1),
        status,
        active_tokens: activeTokens,
        uptime: successRate,
        avg_response_time: Math.round(avgResponseTime),
        success_rate: successRate,
        total_requests: totalRequests,
        error_requests: errorCount,
        last_error: errorCount > 0 ? 'Recent API errors detected' : undefined
      });
    });
    
    return healthData;
  }

  /**
   * Generate error trends over time
   */
  private generateErrorTrends(metrics: any): ErrorTrend[] {
    // For now, generate mock data since we don't have time-series data
    // In a real implementation, this would query historical metrics
    const now = new Date();
    const trends: ErrorTrend[] = [];
    
    for (let i = 23; i >= 0; i--) {
      const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);
      
      // Use actual error counts if available, otherwise mock data
      const googleErrors = this.getErrorCountForProvider(metrics, 'google') || Math.floor(Math.random() * 5);
      const microsoftErrors = this.getErrorCountForProvider(metrics, 'microsoft') || Math.floor(Math.random() * 8);
      const calendlyErrors = this.getErrorCountForProvider(metrics, 'calendly') || Math.floor(Math.random() * 3);
      
      trends.push({
        timestamp: timestamp.toISOString(),
        google_errors: googleErrors,
        microsoft_errors: microsoftErrors,
        calendly_errors: calendlyErrors
      });
    }
    
    return trends;
  }

  /**
   * Get error count for a specific provider
   */
  private getErrorCountForProvider(metrics: any, provider: string): number {
    const errorRequests = metrics.requests?.filter((r: any) => 
      r.provider === provider && r.status === 'error'
    ) || [];
    
    return errorRequests.reduce((sum: number, r: any) => sum + r.value, 0);
  }

  /**
   * Generate token expiration timeline
   */
  private generateExpirationTimeline(): ExpirationData[] {
    // Mock data for token expiration timeline
    // In real implementation, this would query token expiration dates from database
    const timeline: ExpirationData[] = [];
    const now = new Date();
    
    for (let i = 0; i < 30; i++) {
      const date = new Date(now.getTime() + i * 24 * 60 * 60 * 1000);
      const expiringCount = Math.floor(Math.random() * 10);
      
      timeline.push({
        date: date.toISOString().split('T')[0],
        expiring_count: expiringCount
      });
    }
    
    return timeline;
  }

  /**
   * Fetch tenant health data from database
   */
  private async fetchTenantHealth(): Promise<TenantHealth[]> {
    // Mock tenant health data
    // In real implementation, this would fetch from Supabase database
    const mockTenants: TenantHealth[] = [
      {
        tenant_id: 'tenant-1',
        tenant_name: 'Acme Law',
        health_score: 98,
        providers_connected: 3,
        total_providers: 3,
        last_sync: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
        issues: []
      },
      {
        tenant_id: 'tenant-2', 
        tenant_name: 'Smith & Co',
        health_score: 76,
        providers_connected: 2,
        total_providers: 3,
        last_sync: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        issues: ['Token expiring soon']
      },
      {
        tenant_id: 'tenant-3',
        tenant_name: 'Legal Plus',
        health_score: 45,
        providers_connected: 1,
        total_providers: 3,
        last_sync: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        issues: ['Auth failed', 'Sync errors']
      }
    ];
    
    return mockTenants;
  }

  /**
   * Format time ago string
   */
  static formatTimeAgo(timestamp: string): string {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  }

  /**
   * Get health score color class
   */
  static getHealthScoreColor(score: number): string {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  }
}

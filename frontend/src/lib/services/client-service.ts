import { SupabaseClient } from '@supabase/supabase-js';
import { z } from 'zod';

// Client validation schema
export const ClientSchema = z.object({
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address').optional(),
  phone_primary: z.string().optional(),
  client_type: z.enum(['individual', 'business']),
  intake_date: z.string().optional().default(() => new Date().toISOString().split('T')[0]),
  status: z.enum(['active', 'inactive', 'pending']).default('active'),
  business_name: z.string().optional(),
  business_type: z.string().optional(),
  tax_id: z.string().optional(),
  date_of_birth: z.string().optional(),
  address: z.object({
    street: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zip: z.string().optional(),
  }).optional(),
  assigned_attorney_id: z.string().uuid().optional(),
});

// Client update schema (all fields optional)
export const UpdateClientSchema = ClientSchema.partial();

// Client history types
export type ChangeType = 'created' | 'updated' | 'deleted';

export interface ClientHistoryEntry {
  client_id: string;
  tenant_id: string;
  changed_by: string;
  change_type: ChangeType;
  previous_values?: Record<string, unknown>;
  new_values?: Record<string, unknown>;
  created_at?: string;
}

export class ClientService {
  private supabase: SupabaseClient;
  private tenantId: string;

  constructor(supabase: SupabaseClient, tenantId: string) {
    this.supabase = supabase;
    this.tenantId = tenantId;
  }

  /**
   * Helper function to retry a Supabase query with exponential backoff
   */
  private async retryQuery<T>(queryFn: () => Promise<T>, maxRetries = 3): Promise<T> {
    let retries = 0;
    let lastError: any;

    while (retries < maxRetries) {
      try {
        return await queryFn();
      } catch (error) {
        lastError = error;
        retries++;
        console.warn(`Query failed, retry attempt ${retries}/${maxRetries}`, error);

        // Exponential backoff: 500ms, 1000ms, 2000ms, etc.
        const delay = Math.min(500 * Math.pow(2, retries - 1), 5000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    console.error('All retry attempts failed:', lastError);
    throw lastError;
  }

  /**
   * Fetch all clients for the current tenant with optional filtering and pagination
   */
  async getAll(options: {
    page?: number;
    limit?: number;
    status?: string;
    client_type?: string;
    searchTerm?: string;
  } = {}) {
    const {
      page = 1,
      limit = 10,
      status,
      client_type,
      searchTerm
    } = options;

    try {
      return await this.retryQuery(async () => {
        let query = this.supabase
          .schema('tenants')
          .from('clients')
          .select('*, assigned_attorney:assigned_attorney_id(id, email, first_name, last_name)', { count: 'exact' })
          .eq('tenant_id', this.tenantId);

        // Apply filters
        if (status) query = query.eq('status', status);
        if (client_type) query = query.eq('client_type', client_type);

        // Apply search if provided
        if (searchTerm) {
          query = query.or(
            `first_name.ilike.%${searchTerm}%,last_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%,business_name.ilike.%${searchTerm}%`
          );
        }

        // Apply pagination
        const from = (page - 1) * limit;
        const to = from + limit - 1;
        query = query.range(from, to);

        const { data, error, count } = await query;

        if (error) {
          console.error('Error fetching clients:', error);
          throw error;
        }

        return {
          clients: data || [],
          page,
          limit,
          total: count || 0,
          totalPages: count ? Math.ceil(count / limit) : 0,
        };
      });
    } catch (error) {
      console.error('Failed to fetch clients after multiple retries:', error);
      // Return empty result instead of throwing to prevent UI from breaking
      return {
        clients: [],
        page,
        limit,
        total: 0,
        totalPages: 0,
        error: error
      };
    }
  }

  /**
   * Get a single client by ID
   */
  async getById(id: string) {
    try {
      return await this.retryQuery(async () => {
        const { data, error } = await this.supabase
          .schema('tenants')
          .from('clients')
          .select(`
            *,
            assigned_attorney:assigned_attorney_id(id, email, first_name, last_name),
            cases(id, status, created_at)
          `)
          .eq('id', id)
          .eq('tenant_id', this.tenantId)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            throw new Error('Client not found');
          }
          console.error('Error fetching client:', error);
          throw error;
        }

        return data;
      });
    } catch (error) {
      console.error('Failed to fetch client after multiple retries:', error);
      throw error;
    }
  }

  /**
   * Create a new client
   */
  async create(clientData: z.infer<typeof ClientSchema>, userId: string) {
    // Validate the client data
    const validatedData = ClientSchema.parse(clientData);

    const client = {
      ...validatedData,
      tenant_id: this.tenantId,
      created_by: userId,
      created_at: new Date().toISOString(),
    };

    // Start a transaction
    const { data, error } = await this.supabase
      .schema('tenants')
      .from('clients')
      .insert(client)
      .select()
      .single();

    if (error) {
      console.error('Error creating client:', error);
      throw error;
    }

    // Log history entry
    await this.logHistory({
      client_id: data.id,
      tenant_id: this.tenantId,
      changed_by: userId,
      change_type: 'created',
      new_values: data
    });

    return data;
  }

  /**
   * Update an existing client
   */
  async update(id: string, updateData: z.infer<typeof UpdateClientSchema>, userId: string) {
    // Validate the update data
    const validatedData = UpdateClientSchema.parse(updateData);

    // Get the existing client for history
    const { data: existingClient, error: fetchError } = await this.supabase
      .schema('tenants')
      .from('clients')
      .select('*')
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        throw new Error('Client not found');
      }
      console.error('Error fetching client for update:', fetchError);
      throw fetchError;
    }

    // Prepare the update payload
    const payload = {
      ...validatedData,
      updated_by: userId,
      updated_at: new Date().toISOString(),
    };

    // Update the client
    const { data: updatedClient, error: updateError } = await this.supabase
      .schema('tenants')
      .from('clients')
      .update(payload)
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .select(`
        *,
        assigned_attorney:assigned_attorney_id(id, email, first_name, last_name)
      `)
      .single();

    if (updateError) {
      console.error('Error updating client:', updateError);
      throw updateError;
    }

    // Log history entry
    await this.logHistory({
      client_id: id,
      tenant_id: this.tenantId,
      changed_by: userId,
      change_type: 'updated',
      previous_values: existingClient,
      new_values: updatedClient
    });

    return updatedClient;
  }

  /**
   * Delete a client (hard delete or soft delete by marking as inactive)
   */
  async delete(id: string, userId: string, hardDelete: boolean = false) {
    // Get the client to be deleted for history
    const { data: clientToDelete, error: fetchError } = await this.supabase
      .schema('tenants')
      .from('clients')
      .select('*')
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return false; // Client not found
      }
      console.error('Error fetching client for deletion:', fetchError);
      throw fetchError;
    }

    let error;

    if (hardDelete) {
      // Hard delete the client
      const { error: deleteError } = await this.supabase
        .schema('tenants')
        .from('clients')
        .delete()
        .eq('id', id)
        .eq('tenant_id', this.tenantId);

      error = deleteError;
    } else {
      // Soft delete by setting status to inactive
      const { error: updateError } = await this.supabase
        .schema('tenants')
        .from('clients')
        .update({
          status: 'inactive',
          updated_by: userId,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .eq('tenant_id', this.tenantId);

      error = updateError;
    }

    if (error) {
      console.error('Error deleting client:', error);
      throw error;
    }

    // Log history entry
    await this.logHistory({
      client_id: id,
      tenant_id: this.tenantId,
      changed_by: userId,
      change_type: 'deleted',
      previous_values: clientToDelete
    });

    return true;
  }

  /**
   * Get clients by IDs (bulk fetch)
   */
  async getByIds(ids: string[]) {
    if (!ids.length) return [];

    const { data, error } = await this.supabase
      .schema('tenants')
      .from('clients')
      .select('*, assigned_attorney:assigned_attorney_id(id, email, first_name, last_name)')
      .in('id', ids)
      .eq('tenant_id', this.tenantId);

    if (error) {
      console.error('Error fetching clients by IDs:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Log a history entry for client changes
   */
  private async logHistory(historyEntry: ClientHistoryEntry) {
    const { error } = await this.supabase
      .schema('tenants')
      .from('client_history')
      .insert({
        ...historyEntry,
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error logging client history:', error);
      // Don't throw error for history logging failures
      // to avoid disrupting the main transaction
    }
  }
}

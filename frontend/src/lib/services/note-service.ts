// src/lib/services/note-service.ts
import type { SupabaseClient } from '@supabase/supabase-js';
import { z } from 'zod';

// Placeholder Note type (adjust if you have a real one defined elsewhere)
export interface Note {
  id: string;
  case_id: string;
  created_by: string;
  title: string;
  content: string;
  category: string;
  tags?: string[];
  metadata?: Record<string, unknown>;
  created_at: string;
  updated_at?: string;
}

// Placeholder Note schema (matching the one in the API route)
const NoteSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Content is required'),
  category: z.string().min(1, 'Category is required'),
  tags: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  case_id: z.string().uuid('Invalid case ID'),
});

export function createNoteService(supabase: SupabaseClient, tenantId: string) {
  console.log('Initializing STUB NoteService for tenant:', tenantId);

  return {
    /**
     * STUB: Fetch notes by case ID. Needs implementation.
     */
    async getByCaseId(caseId: string, options: {
      searchTerm?: string;
      category?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}): Promise<Note[]> {
      console.warn(`STUB: NoteService.getByCaseId called for case ${caseId} with options:`, options);
      // In a real implementation, query Supabase 'notes' table
      // Filter by caseId, tenantId, searchTerm, category
      // Sort by sortBy, sortOrder
      return []; // Return empty array for now
    },

    /**
     * STUB: Create a new note. Needs implementation.
     */
    async create(noteData: z.infer<typeof NoteSchema>, userId: string): Promise<Note> {
        console.warn(`STUB: NoteService.create called by user ${userId} with data:`, noteData);

        // In a real implementation:
        // 1. Validate case_id exists and belongs to the tenant
        // 2. Insert into Supabase 'notes' table with tenantId and created_by = userId
        // 3. Return the created note object

        // Throwing an error clearly indicates it's not implemented
        throw new Error(`STUB: Note creation not implemented yet.`);

        /* Alternatively, return a dummy object:
        return {
          id: crypto.randomUUID(),
          case_id: noteData.case_id,
          created_by: userId,
          title: noteData.title,
          content: noteData.content,
          category: noteData.category,
          metadata: noteData.metadata || {},
          tags: noteData.tags ? noteData.tags.split(',').map(t=>t.trim()) : [],
          created_at: new Date().toISOString(),
        };
        */
    },

    // Add other placeholder methods if needed (e.g., update, delete)
    // async update(...) { ... }
    // async delete(...) { ... }
  };
}

export type NoteService = ReturnType<typeof createNoteService>;

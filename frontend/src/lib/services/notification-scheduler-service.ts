import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../supabase/database.types';
import { NotificationService } from './notification-service';
import { EmailService } from './email-service';
import { TwilioService } from './twilio-service';
import {
  createNotificationScheduleClient,
  NotificationScheduleRow
} from './notification-schedule-client';

export interface NotificationSchedule {
  id: string;
  tenantId: string;
  userId: string;
  type: string;
  scheduledFor: string;
  status: 'pending' | 'sent' | 'failed';
  data: any;
  createdAt: string;
  sentAt?: string;
}

/**
 * Map a database row to our DTO format
 */
function mapScheduleRowToDto(row: NotificationScheduleRow): NotificationSchedule {
  return {
    id: row.id,
    tenantId: row.tenant_id,
    userId: row.user_id,
    type: row.type,
    scheduledFor: row.scheduled_for,
    status: row.status,
    data: row.data,
    createdAt: row.created_at,
    sentAt: row.sent_at || undefined,
  };
}

export class NotificationSchedulerService {
  private notificationService: NotificationService;
  private emailService: EmailService;
  private twilioService: TwilioService;
  private scheduleClient;

  constructor(private supabase: SupabaseClient<Database>) {
    this.notificationService = new NotificationService(supabase);
    this.emailService = new EmailService(supabase);
    this.twilioService = new TwilioService(supabase);
    this.scheduleClient = createNotificationScheduleClient(supabase);
  }

  /**
   * Schedule a notification
   * @param tenantId The tenant ID
   * @param userId The user ID
   * @param type The notification type
   * @param scheduledFor When to send the notification
   * @param data Additional data for the notification
   * @returns The created notification schedule
   */
  async scheduleNotification(
    tenantId: string,
    userId: string,
    type: string,
    scheduledFor: Date,
    data: Record<string, unknown>
  ): Promise<NotificationSchedule> {
    const schedule = await this.scheduleClient.createNotificationSchedule({
      tenant_id: tenantId,
      user_id: userId,
      type,
      scheduled_for: scheduledFor.toISOString(),
      status: 'pending',
      data,
    });

    return mapScheduleRowToDto(schedule);
  }

  /**
   * Schedule a trial ending notification
   * @param tenantId The tenant ID
   * @param userId The user ID
   * @param trialEndDate The trial end date
   * @param daysBeforeEnd Days before the end to send the notification
   * @returns The created notification schedule
   */
  async scheduleTrialEndingNotification(
    tenantId: string,
    userId: string,
    trialEndDate: Date,
    daysBeforeEnd: number = 3
  ): Promise<NotificationSchedule> {
    // Calculate when to send the notification
    const scheduledFor = new Date(trialEndDate);
    scheduledFor.setDate(scheduledFor.getDate() - daysBeforeEnd);

    return this.scheduleNotification(
      tenantId,
      userId,
      'subscription_trial_ending',
      scheduledFor,
      {
        daysRemaining: daysBeforeEnd,
        trialEndDate: trialEndDate.toISOString(),
      }
    );
  }

  /**
   * Schedule a quota approaching notification
   * @param tenantId The tenant ID
   * @param userId The user ID
   * @param quotaType The type of quota
   * @param percentThreshold The percentage threshold to trigger the notification
   * @returns The created notification schedule
   */
  async scheduleQuotaApproachingNotification(
    tenantId: string,
    userId: string,
    quotaType: string,
    percentThreshold: number = 80
  ): Promise<NotificationSchedule> {
    // Schedule for immediate delivery
    const scheduledFor = new Date();

    return this.scheduleNotification(
      tenantId,
      userId,
      'quota_limit_approaching',
      scheduledFor,
      {
        quotaType,
        percentUsed: percentThreshold,
      }
    );
  }

  /**
   * Process pending notifications
   * @returns The number of notifications processed
   */
  async processPendingNotifications(): Promise<number> {
    // Get pending notifications that are due
    const pendingNotifications = await this.scheduleClient.getPendingNotifications(50);

    if (pendingNotifications.length === 0) {
      return 0;
    }

    let processedCount = 0;

    // Process each notification
    for (const notification of pendingNotifications) {
      try {
        // For now, we'll enable all notification types by default
        // In a real implementation, we would fetch user preferences from the database
        const userPreferences = {
          emailEnabled: true,
          smsEnabled: false,
          inAppEnabled: true,
        };

        // Send in-app notification
        if (userPreferences.inAppEnabled) {
          await this.sendInAppNotification(notification);
        }

        // Send email notification
        if (userPreferences.emailEnabled) {
          // In a real implementation, we would fetch the user's email from the database
          // For now, we'll skip this step in the test implementation
          // await this.sendEmailNotification(notification);
        }

        // Send SMS notification
        if (userPreferences.smsEnabled) {
          // In a real implementation, we would fetch the user's phone from the database
          // For now, we'll skip this step in the test implementation
          // await this.sendSmsNotification(notification);
        }

        // Mark as sent
        await this.scheduleClient.markAsSent(notification.id);

        processedCount++;
      } catch (error) {
        console.error(`Error processing notification ${notification.id}:`, error);

        // Mark as failed
        await this.scheduleClient.markAsFailed(notification.id);
      }
    }

    return processedCount;
  }

  /**
   * Send an in-app notification
   * @param notification The notification schedule
   */
  private async sendInAppNotification(notification: any): Promise<void> {
    let message = '';
    let severity: 'low' | 'medium' | 'high' = 'low';

    // Generate message based on notification type
    switch (notification.type) {
      case 'subscription_trial_ending':
        message = `Your trial will end in ${notification.data.daysRemaining} day${notification.data.daysRemaining === 1 ? '' : 's'}. Please update your payment information to continue using the service.`;
        severity = notification.data.daysRemaining <= 3 ? 'high' : 'medium';
        break;

      case 'quota_limit_approaching':
        message = `You have used ${notification.data.percentUsed}% of your ${notification.data.quotaType} quota for this period.`;
        severity = notification.data.percentUsed >= 90 ? 'high' : 'medium';
        break;

      case 'quota_limit_reached':
        message = `You have reached your ${notification.data.quotaType} quota for this period. Please upgrade your plan to continue.`;
        severity = 'high';
        break;

      case 'subscription_payment_failed':
        message = 'Your subscription payment has failed. Please update your payment information to avoid service interruption.';
        severity = 'high';
        break;

      case 'subscription_renewed':
        message = `Your subscription has been renewed. You have been charged $${notification.data.amount.toFixed(2)}.`;
        severity = 'low';
        break;

      default:
        message = notification.data.message || 'You have a new notification.';
        break;
    }

    // Create the in-app notification
    await this.notificationService.createNotification(
      notification.tenant_id,
      notification.user_id,
      notification.type,
      message,
      severity,
      notification.data
    );
  }

  /**
   * Send an email notification
   * @param notification The notification schedule
   */
  private async sendEmailNotification(notification: any): Promise<void> {
    const email = notification.user?.email;
    const tenantName = notification.tenant?.name || 'Your Account';
    const userName = notification.user?.full_name || 'User';

    // Send email based on notification type
    switch (notification.type) {
      case 'subscription_trial_ending':
        await this.emailService.sendTrialEndingNotification(
          email,
          notification.data.daysRemaining,
          tenantName,
          userName
        );
        break;

      case 'quota_limit_approaching':
        await this.emailService.sendQuotaApproachingNotification(
          email,
          notification.data.quotaType,
          notification.data.percentUsed,
          tenantName,
          userName
        );
        break;

      case 'quota_limit_reached':
        await this.emailService.sendQuotaReachedNotification(
          email,
          notification.data.quotaType,
          tenantName,
          userName
        );
        break;

      case 'subscription_payment_failed':
        await this.emailService.sendPaymentFailedNotification(
          email,
          tenantName,
          userName
        );
        break;

      case 'subscription_renewed':
        await this.emailService.sendSubscriptionRenewedNotification(
          email,
          tenantName,
          userName,
          notification.data.amount,
          new Date(notification.data.nextBillingDate)
        );
        break;
    }
  }

  /**
   * Send an SMS notification
   * @param notification The notification schedule
   */
  private async sendSmsNotification(notification: any): Promise<void> {
    const phone = notification.user?.phone;
    const tenantName = notification.tenant?.name || 'Your Account';

    // Send SMS based on notification type
    switch (notification.type) {
      case 'subscription_trial_ending':
        await this.twilioService.sendTrialEndingNotification(
          phone,
          notification.data.daysRemaining,
          tenantName
        );
        break;

      case 'quota_limit_approaching':
        await this.twilioService.sendQuotaApproachingNotification(
          phone,
          notification.data.quotaType,
          notification.data.percentUsed,
          tenantName
        );
        break;

      case 'quota_limit_reached':
        await this.twilioService.sendQuotaReachedNotification(
          phone,
          notification.data.quotaType,
          tenantName
        );
        break;

      case 'subscription_payment_failed':
        await this.twilioService.sendPaymentFailedNotification(
          phone,
          tenantName
        );
        break;

      case 'subscription_renewed':
        await this.twilioService.sendSubscriptionRenewedNotification(
          phone,
          tenantName,
          notification.data.amount
        );
        break;
    }
  }
}

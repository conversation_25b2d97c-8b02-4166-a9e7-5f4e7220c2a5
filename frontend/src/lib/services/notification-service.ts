import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../supabase/database.types';
import { createNotificationClient, NotificationRow } from './notification-client';

export type NotificationType =
  | 'info'
  | 'warning'
  | 'error'
  | 'success'
  | 'subscription_trial_ending'
  | 'subscription_payment_failed'
  | 'subscription_renewed'
  | 'quota_limit_approaching'
  | 'quota_limit_reached'
  | 'security_alert'
  | 'system_message';

export type NotificationSeverity = 'low' | 'medium' | 'high';

export interface Notification {
  id: string;
  tenantId: string;
  userId: string;
  type: NotificationType;
  message: string;
  read: boolean;
  severity: NotificationSeverity;
  context?: any;
  senderId?: string;
  createdAt: string;
  updatedAt?: string;
}

/**
 * Helper to map a database row to our DTO format
 */
function mapNotificationRowToDto(row: NotificationRow): Notification {
  return {
    id: row.id,
    tenantId: row.tenant_id,
    userId: row.user_id,
    type: row.type as NotificationType,
    message: row.message,
    read: row.read,
    severity: row.severity as NotificationSeverity,
    context: row.context,
    senderId: row.sender_id,
    createdAt: row.created_at,
    updatedAt: row.updated_at,
  };
}

export class NotificationService {
  private notificationClient;

  constructor(private supabase: SupabaseClient<Database>) {
    this.notificationClient = createNotificationClient(supabase);
  }

  /**
   * Create a new notification
   * @param tenantId The tenant ID
   * @param userId The user ID
   * @param type The notification type
   * @param message The notification message
   * @param severity The notification severity
   * @param context Additional context for the notification
   * @returns The created notification
   */
  async createNotification(
    tenantId: string,
    userId: string,
    type: NotificationType,
    message: string,
    severity: NotificationSeverity = 'low',
    context?: Record<string, unknown>
  ): Promise<Notification> {
    const newNotification = await this.notificationClient.createNotification({
      tenant_id: tenantId,
      user_id: userId,
      type,
      message,
      read: false,
      severity,
      context,
      created_at: new Date().toISOString(),
    });

    return mapNotificationRowToDto(newNotification);
  }

  /**
   * Get notifications for a user
   * @param userId The user ID
   * @param includeRead Whether to include read notifications
   * @param limit The maximum number of notifications to return
   * @returns A list of notifications
   */
  async getUserNotifications(
    userId: string,
    includeRead: boolean = false,
    limit: number = 10
  ): Promise<Notification[]> {
    const notifications = await this.notificationClient.getUserNotifications(
      userId,
      includeRead,
      limit
    );

    return notifications.map(mapNotificationRowToDto);
  }

  /**
   * Mark a notification as read
   * @param notificationId The notification ID
   * @param userId The user ID
   * @returns The updated notification
   */
  async markAsRead(notificationId: string, userId: string): Promise<{ id: string; read: boolean; updatedAt: string }> {
    const result = await this.notificationClient.markAsRead(notificationId, userId);

    return {
      id: result.id,
      read: result.read,
      updatedAt: result.updated_at,
    };
  }

  /**
   * Mark all notifications as read for a user
   * @param userId The user ID
   */
  async markAllAsRead(userId: string): Promise<void> {
    await this.notificationClient.markAllAsRead(userId);
  }

  /**
   * Delete a notification
   * @param notificationId The notification ID
   * @param userId The user ID
   */
  async deleteNotification(notificationId: string, userId: string): Promise<void> {
    await this.notificationClient.deleteNotification(notificationId, userId);
  }

  /**
   * Get the count of unread notifications for a user
   * @param userId The user ID
   * @returns The count of unread notifications
   */
  async getUnreadCount(userId: string): Promise<number> {
    return this.notificationClient.getUnreadCount(userId);
  }
}

// src/lib/services/task-service.ts
import type { SupabaseClient } from '@supabase/supabase-js';
import { createUserService, UserService } from './user-service';


export type TaskStatus = 'todo' | 'in_progress' | 'done';

export interface Task {
  id: string;
  title: string;
  description?: string;
  due_date?: string;
  status: TaskStatus;
  tenant_id: string;
  assigned_to?: string; // users.id (not auth_user_id)
  related_case?: string;
  created_by: string; // users.id (not auth_user_id)
  created_at: string;
  updated_by?: string; // users.id (not auth_user_id)
  updated_at?: string;
  ai_metadata?: any;
  // Fields for triggering automatic deadline calculation
  jurisdiction_id?: string; // e.g., 'tx-civil'
  trigger_date?: string; // YYYY-MM-DD
  rule_id?: string; // Identifier for the specific rule
}

export interface TaskWithRelations extends Task {
  assigned_to_user?: any;
  related_case_info?: any;
  creator?: any;
  updater?: any;
}

/**
 * Task Service
 * Responsible for all task-related data operations
 */
export class TaskService {
  private supabase: SupabaseClient;
  private tenantId: string;
  private userService: UserService;

  constructor(supabase: SupabaseClient, tenantId: string) {
    this.supabase = supabase;
    this.tenantId = tenantId;
    this.userService = createUserService(supabase, tenantId);
  }

  /**
   * Get all tasks for the current tenant with related data
   */
  async getAll(): Promise<TaskWithRelations[]> {
    try {
      // First, fetch all tasks
      const { data: tasks, error } = await this.supabase
        .schema('tenants')
        .from('tasks')
        .select('*')
        .eq('tenant_id', this.tenantId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching tasks:', error);
        throw error;
      }

      // Get unique IDs of all related users
      const userIds = new Set<string>();
      tasks.forEach(task => {
        if (task.assigned_to) userIds.add(task.assigned_to);
        if (task.created_by) userIds.add(task.created_by);
        if (task.updated_by) userIds.add(task.updated_by);
      });

      // Fetch all users in bulk (more efficient than individual queries)
      const users = await this.userService.getByIds(Array.from(userIds));
      const userMap = new Map(users.map(user => [user.id, user]));

      // Get unique IDs of all related cases
      const caseIds = new Set<string>();
      tasks.forEach(task => {
        if (task.related_case) caseIds.add(task.related_case);
      });

      // Fetch all cases in bulk if needed
      let casesMap = new Map();
      if (caseIds.size > 0) {
        const { data: cases, error: casesError } = await this.supabase
          .schema('tenants')
          .from('cases')
          .select('id, title')
          .in('id', Array.from(caseIds));

        if (!casesError && cases) {
          casesMap = new Map(cases.map(caseItem => [caseItem.id, caseItem]));
        }
      }

      // Map the related data to each task
      return tasks.map(task => {
        const taskWithRelations: TaskWithRelations = {
          ...task,
          created_at: new Date(task.created_at).toISOString(),
          updated_at: task.updated_at ? new Date(task.updated_at).toISOString() : undefined,
          due_date: task.due_date ? new Date(task.due_date).toISOString() : undefined,
        };

        // Attach related user data
        if (task.assigned_to && userMap.has(task.assigned_to)) {
          taskWithRelations.assigned_to_user = userMap.get(task.assigned_to);
        }

        if (task.created_by && userMap.has(task.created_by)) {
          taskWithRelations.creator = userMap.get(task.created_by);
        }

        if (task.updated_by && userMap.has(task.updated_by)) {
          taskWithRelations.updater = userMap.get(task.updated_by);
        }

        // Attach related case data
        if (task.related_case && casesMap.has(task.related_case)) {
          taskWithRelations.related_case_info = casesMap.get(task.related_case);
        }

        return taskWithRelations;
      });
    } catch (error) {
      console.error('Exception in TaskService.getAll:', error);
      throw error;
    }
  }

  /**
   * Get a task by ID with all related data
   */
  async getById(taskId: string): Promise<TaskWithRelations | null> {
    try {
      const { data: task, error } = await this.supabase
        .schema('tenants')
        .from('tasks')
        .select('*')
        .eq('id', taskId)
        .eq('tenant_id', this.tenantId)
        .single();

      if (error) {
        console.error(`Error fetching task by ID ${taskId}:`, error);
        return null;
      }

      // Fetch related user data
      const userIds = [
        task.assigned_to,
        task.created_by,
        task.updated_by
      ].filter(Boolean) as string[];

      const users = await this.userService.getByIds(userIds);
      const userMap = new Map(users.map(user => [user.id, user]));

      // Fetch related case data if needed
      let relatedCase = null;
      if (task.related_case) {
        const { data: caseData, error: caseError } = await this.supabase
          .schema('tenants')
          .from('cases')
          .select('id, title')
          .eq('id', task.related_case)
          .single();

        if (!caseError && caseData) {
          relatedCase = caseData;
        }
      }

      // Build the full task object with relations
      const taskWithRelations: TaskWithRelations = {
        ...task,
        created_at: new Date(task.created_at).toISOString(),
        updated_at: task.updated_at ? new Date(task.updated_at).toISOString() : undefined,
        due_date: task.due_date ? new Date(task.due_date).toISOString() : undefined,
      };

      // Attach related user data
      if (task.assigned_to && userMap.has(task.assigned_to)) {
        taskWithRelations.assigned_to_user = userMap.get(task.assigned_to);
      }

      if (task.created_by && userMap.has(task.created_by)) {
        taskWithRelations.creator = userMap.get(task.created_by);
      }

      if (task.updated_by && userMap.has(task.updated_by)) {
        taskWithRelations.updater = userMap.get(task.updated_by);
      }

      // Attach related case data
      if (relatedCase) {
        taskWithRelations.related_case_info = relatedCase;
      }

      return taskWithRelations;
    } catch (error) {
      console.error('Exception in TaskService.getById:', error);
      return null;
    }
  }

  /**
   * Create a new task
   * @param userId The ID of the user creating the task (users.id, not auth_user_id)
   */
  async create(userId: string, data: Partial<Task>): Promise<TaskWithRelations | null> {
    try {
      // Validate essential fields
      if (!data.title) {
        throw new Error('Task title is required');
      }

      // Prepare task data
      const taskData = {
        ...data,
        tenant_id: this.tenantId,
        created_by: userId, // Using users.id, not auth_user_id
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: data.status || 'todo'
      };

      // Insert the new task
      const { data: createdTask, error } = await this.supabase
        .schema('tenants')
        .from('tasks')
        .insert(taskData)
        .select('*')
        .single();

      if (error) {
        console.error('Error creating task:', error);
        throw error;
      }

      // Log task creation in history
      await this.supabase
        .schema('tenants')
        .from('task_history')
        .insert({
          task_id: createdTask.id,
          tenant_id: this.tenantId,
          changed_by: userId, // Using users.id, not auth_user_id
          change_type: 'created',
          new_values: createdTask,
        });

      // TODO: Add deadline calculation logic here if needed

      // Return the created task with related data
      return await this.getById(createdTask.id);
    } catch (error) {
      console.error('Exception in TaskService.create:', error);
      throw error;
    }
  }

  /**
   * Update an existing task
   * @param taskId The ID of the task to update
   * @param userId The ID of the user making the update (users.id, not auth_user_id)
   */
  async update(taskId: string, userId: string, data: Partial<Task>): Promise<TaskWithRelations | null> {
    try {
      // Fetch the existing task first
      const { data: existingTask, error: fetchError } = await this.supabase
        .schema('tenants')
        .from('tasks')
        .select('*')
        .eq('id', taskId)
        .eq('tenant_id', this.tenantId)
        .single();

      if (fetchError || !existingTask) {
        console.error(`Error fetching task ${taskId} for update:`, fetchError);
        return null;
      }

      // Prepare update data
      const updateData = {
        ...data,
        updated_by: userId, // Using users.id, not auth_user_id
        updated_at: new Date().toISOString(),
      };

      // Update the task
      const { data: updatedTask, error } = await this.supabase
        .schema('tenants')
        .from('tasks')
        .update(updateData)
        .eq('id', taskId)
        .eq('tenant_id', this.tenantId)
        .select('*')
        .single();

      if (error) {
        console.error(`Error updating task ${taskId}:`, error);
        throw error;
      }

      // Log task update in history
      await this.supabase
        .schema('tenants')
        .from('task_history')
        .insert({
          task_id: taskId,
          tenant_id: this.tenantId,
          changed_by: userId, // Using users.id, not auth_user_id
          change_type: 'updated',
          previous_values: existingTask,
          new_values: updatedTask,
        });

      // TODO: Add deadline recalculation logic here if needed

      // Return the updated task with related data
      return await this.getById(taskId);
    } catch (error) {
      console.error('Exception in TaskService.update:', error);
      throw error;
    }
  }

  /**
   * Delete a task
   * @param taskId The ID of the task to delete
   * @param userId The ID of the user performing the deletion (users.id, not auth_user_id)
   */
  async delete(taskId: string, userId: string): Promise<boolean> {
    try {
      // Fetch the task first for history
      const { data: taskToDelete, error: fetchError } = await this.supabase
        .schema('tenants')
        .from('tasks')
        .select('*')
        .eq('id', taskId)
        .eq('tenant_id', this.tenantId)
        .single();

      if (fetchError || !taskToDelete) {
        console.error(`Error fetching task ${taskId} for deletion:`, fetchError);
        return false;
      }

      // Delete the task
      const { error } = await this.supabase
        .schema('tenants')
        .from('tasks')
        .delete()
        .eq('id', taskId)
        .eq('tenant_id', this.tenantId);

      if (error) {
        console.error(`Error deleting task ${taskId}:`, error);
        return false;
      }

      // Log task deletion in history
      await this.supabase
        .schema('tenants')
        .from('task_history')
        .insert({
          task_id: taskId,
          tenant_id: this.tenantId,
          changed_by: userId, // Using users.id, not auth_user_id
          change_type: 'deleted',
          previous_values: taskToDelete,
        });

      return true;
    } catch (error) {
      console.error('Exception in TaskService.delete:', error);
      return false;
    }
  }

  /**
   * Get task history
   */
  async getHistory(taskId: string): Promise<any[]> {
    try {
      const { data, error } = await this.supabase
        .schema('tenants')
        .from('task_history')
        .select('*')
        .eq('task_id', taskId)
        .eq('tenant_id', this.tenantId)
        .order('changed_at', { ascending: false });

      if (error) {
        console.error(`Error fetching history for task ${taskId}:`, error);
        return [];
      }

      // Fetch user information for all history entries
      const userIds = data
        .map(entry => entry.changed_by)
        .filter(Boolean) as string[];

      const uniqueUserIds = [...new Set(userIds)];
      const users = await this.userService.getByIds(uniqueUserIds);
      const userMap = new Map(users.map(user => [user.id, user]));

      // Enrich history entries with user data
      return data.map(entry => {
        const enrichedEntry = {
          ...entry,
          changed_at: new Date(entry.changed_at).toISOString(),
        };

        if (entry.changed_by && userMap.has(entry.changed_by)) {
          enrichedEntry.changed_by_user = userMap.get(entry.changed_by);
        }

        return enrichedEntry;
      });
    } catch (error) {
      console.error(`Exception in TaskService.getHistory for task ${taskId}:`, error);
      return [];
    }
  }
}

/**
 * Factory function to create a TaskService instance
 */
export function createTaskService(supabase: SupabaseClient, tenantId: string) {
  return new TaskService(supabase, tenantId);
}

import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from './database.types';

/**
 * Type definition for the tenant schema 'from' method
 * This allows us to provide proper TypeScript typing for tenant schema tables
 */
export type TenantSchemaFrom = <T extends string>(table: T) => ReturnType<SupabaseClient<Database>['from']>;

/**
 * Generic type for tenant database records
 */
export type TenantRecord = Record<string, unknown>;

/**
 * Creates a client specifically for accessing tenant schema tables
 * This provides type-safe access to tenant schema while working around TypeScript limitations
 *
 * @param client The Supabase client
 * @returns A tenant database client with type-safe methods
 */
export function createTenantClient(client: SupabaseClient<Database>) {
  return {
    /**
     * Get access to the tenant schema
     * Uses type assertion to make TypeScript happy while providing proper typing
     */
    tenantSchema() {
      return (client as any).schema('tenants') as {
        from: TenantSchemaFrom
      };
    },

    /**
     * Safely execute a query against the tenant schema
     * This is a helper method to reduce boilerplate in schema queries
     *
     * @param tableName The tenant schema table name
     * @param queryFn A function that builds and executes the query
     * @returns The result of the query
     */
    async tenantQuery<T>(
      tableName: string,
      queryFn: (query: ReturnType<SupabaseClient<Database>['from']>) => Promise<{ data: T; error: any }>
    ): Promise<T> {
      const query = (client as any).schema('tenants').from(tableName);
      const { data, error } = await queryFn(query);

      if (error) throw error;
      return data;
    }
  };
}

/**
 * Type definitions for CopilotKit v2.x with AG-UI protocol
 * 
 * This file provides TypeScript definitions compatible with CopilotKit v2.x
 * to avoid TypeScript errors in our implementation
 */

import { z } from 'zod';

// Basic TypeScript types for AG-UI parameters
export interface ObjectParameter {
  type: 'object';
  properties: Record<string, unknown>;
  required?: string[];
  description?: string;
}

export interface StringParameter {
  type: 'string';
  description?: string;
  enum?: string[];
}

export interface NumberParameter {
  type: 'number';
  description?: string;
  minimum?: number;
  maximum?: number;
}

export interface BooleanParameter {
  type: 'boolean';
  description?: string;
}

export interface ArrayParameter {
  type: 'array';
  description?: string;
  items: Parameter;
}

export type Parameter = 
  | ObjectParameter
  | StringParameter
  | NumberParameter
  | BooleanParameter
  | ArrayParameter;

// Parameter schema type for AG-UI actions
export interface ParameterSchema {
  type: 'object';
  properties: Record<string, Parameter>;
  required: string[];
}

// Handler type for AG-UI actions
export type ActionHandler = (args: any) => Promise<{
  ok: boolean;
  message: string;
  [key: string]: any;
}>;

// Configuration for AG-UI actions
export interface AGUIAction {
  name: string;
  description: string;
  parameters: ParameterSchema;
  handler: ActionHandler;
}

/**
 * Validates input against a Zod schema and returns the validated data
 * @param input The input data to validate
 * @param schema The Zod schema to validate against
 * @returns The validated data
 * @throws Error if validation fails
 */
export function validateInput<T>(input: any, schema: z.ZodType<T>): T {
  const result = schema.safeParse(input);
  if (!result.success) {
    throw new Error(
      `Validation failed: ${result.error.errors.map(e => e.message).join(', ')}`
    );
  }
  return result.data;
}

/**
 * Creates a standardized success response for AG-UI actions
 * @param message The success message
 * @param data Additional data to include in the response
 * @returns A formatted success response
 */
export function createSuccess(message: string, data?: Record<string, unknown>) {
  return {
    ok: true,
    message,
    ...(data || {})
  };
}

/**
 * Creates a standardized error for AG-UI actions
 * @param message The error message
 * @param details Optional error details
 * @throws An error with the specified message and details
 */
export function createError(message: string, details?: Record<string, unknown>): never {
  const error = new Error(message);
  if (details) {
    (error as any).details = details;
  }
  throw error;
}

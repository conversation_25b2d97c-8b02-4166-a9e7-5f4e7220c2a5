/**
 * Authentication and User Types
 * This file contains types related to authentication and user information
 */

/**
 * Represents the possible roles a user can have within the system.
 * Enum members are PascalCase (for linting), while string values are lowercase (for DB/JWT).
 */
export enum UserRole {
  Authenticated = 'authenticated',
  Superadmin = 'superadmin',
  Partner = 'partner',
  Attorney = 'attorney',
  Paralegal = 'paralegal',
  Staff = 'staff',
  Client = 'client',
  Admin = 'admin',
}

/**
 * Authenticated user information
 * Represents the user data available after authentication
 */
export interface AuthUser {
  /**
   * User's unique identifier
   */
  id: string;

  /**
   * User's email address
   */
  email: string;

  /**
   * User's role in the system
   */
  role: UserRole;

  /**
   * Tenant identifier (for multi-tenancy)
   */
  tenantId: string | null;

  /**
   * Optional metadata
   */
  metadata?: Record<string, unknown>;

  /**
   * Optional token expiration timestamp
   */
  exp?: number;

  /**
   * Optional first name
   */
  firstName?: string;

  /**
   * Optional last name
   */
  lastName?: string;
}

/**
 * Type guard to check if a string is a valid UserRole
 *
 * @param role The role to check
 * @returns True if the role is a valid UserRole
 */
export function isValidUserRole(role: unknown): role is UserRole {
  return typeof role === 'string' &&
    ['authenticated', 'superadmin', 'partner', 'attorney', 'paralegal', 'staff', 'client', 'admin'].includes(role as string);
}

/**
 * Authentication result
 * Represents the result of an authentication operation
 */
export interface AuthResult {
  /**
   * Whether the authentication was successful
   */
  success: boolean;

  /**
   * The authenticated user (if successful)
   */
  user?: AuthUser;

  /**
   * Error message (if unsuccessful)
   */
  error?: string;
}

/**
 * JWT Claims
 * Represents the claims in a JWT token
 */
export interface JwtClaims {
  /**
   * Subject (user ID)
   */
  sub: string;

  /**
   * Email address
   */
  email?: string;

  /**
   * Roles
   */
  roles?: UserRole[];

  /**
   * Role (singular)
   */
  role?: UserRole;

  /**
   * Tenant ID
   */
  tenant_id?: string;

  /**
   * Expiration time
   */
  exp?: number;

  /**
   * Issued at time
   */
  iat?: number;

  /**
   * JWT ID
   */
  jti?: string;

  /**
   * Additional claims
   */
  [key: string]: any;
}

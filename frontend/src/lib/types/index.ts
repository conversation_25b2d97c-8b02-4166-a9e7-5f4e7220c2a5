/**
 * Common Type Definitions for the Application
 */

/**
 * Authenticated User Information
 */
export interface AuthUser {
  id: string;
  email: string;
  role: string;
  tenantId: string;
  metadata?: Record<string, unknown>;
}

/**
 * User Activity Types
 */
export type ActivityType =
  | 'DOCUMENT_UPLOAD'
  | 'DOCUMENT_VIEW'
  | 'EMAIL_SENT'
  | 'CASE_VIEWED'
  | 'CASE_UPDATED'
  | 'TASK_COMPLETED'
  | 'NOTE_ADDED'
  | 'DEADLINE_APPROACHING'
  | 'CLIENT_CONTACTED'
  | 'MEETING_SCHEDULED';

/**
 * Activity Context
 * Provides detailed context about a user activity.
 */
export interface ActivityContext {
  component: string;        // e.g., 'CaseDetails', 'DocumentViewer', 'TaskBoard'
  action: string;           // e.g., 'VIEWED_CASE', 'UPDATED_TASK_STATUS', 'SENT_EMAIL'
  entityType?: string;      // e.g., 'case', 'document', 'task', 'contact'
  entityId?: string;        // The ID of the primary entity involved
  metadata?: Record<string, unknown>; // Additional contextual data (e.g., { taskId: '...', newStatus: 'done' })
}

/**
 * Neo4j Activity Record
 */
export interface ActivityRecord {
  time: string;
  summary: string;
  actionType: ActivityType;
  caseTitle?: string;
  activityId: string;
  metadata?: Record<string, unknown>;
}

/**
 * Proactive Message Configuration
 */
export interface ProactiveMessageConfig {
  userId: string;
  lastLoginDate?: string;
  daysBack?: number;
  limit?: number;
}

import { UserRole } from './auth';
export { UserRole }; // Re-export UserRole for use throughout the application

/**
 * Represents the data structure for an enriched activity log entry.
 */
export interface Activity {
  activityId: string;
  userId: string;
  userRole: UserRole;
  tenantId?: string;
  time: string; // ISO 8601 format
  action: string;
  category: string;
  details?: Record<string, unknown>;
  importance?: 'high' | 'medium' | 'low' | 'unknown';
  tags?: string[];
  summary?: string;
  llmPrompt?: string; // The prompt used for analysis
  llmResponse?: string; // Raw LLM response (optional)
  llmResponseFormat?: 'json' | 'text'; // Format of the response
  caseId?: string;
  caseTitle?: string | null;
  documentId?: string;
  documentName?: string | null;
}

// Represents a processed, actionable insight derived from user activities.
export interface Insight {
  id: string; // Use activityId or generate a new one
  message: string;
  suggestions: string[];
  priority: number; // Higher number = higher priority (e.g., 10 for high, 5 for medium)
  relatedEntity?: {
    type: string; // e.g., 'case', 'document', 'contact'
    id?: string;
    name?: string | null;
  };
  timestamp: string; // Original activity timestamp
  groupKey?: string; // For grouping related activities (e.g., case name, deadline date)
  relatedActivities?: string[]; // IDs of related activities
  feedbackId?: string; // ID for tracking user feedback
  aiGenerated?: boolean; // Flag to indicate if this was generated by AI
}

/**
 * Represents a user within the application, typically derived from JWT claims.
 */

export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

/**
 * User preferences for personalization and customization
 */
export interface UserPreferences {
  // General preferences
  theme?: 'light' | 'dark' | 'system';
  language?: string;
  timezone?: string;

  // Notification preferences
  emailNotifications?: boolean;
  pushNotifications?: boolean;

  // AI and insights preferences
  aiEnabled?: boolean;
  insightFrequency?: 'high' | 'medium' | 'low';
  insightCategories?: string[];
  dismissedInsights?: string[];

  // Dashboard preferences
  defaultDashboardView?: 'cases' | 'tasks' | 'calendar' | 'insights';
  dashboardWidgets?: string[];

  // Case management preferences
  caseViewPreferences?: {
    sortBy?: string;
    filterBy?: string[];
    groupBy?: string;
  };

  // Custom fields that can be extended based on user role
  customPreferences?: Record<string, unknown>;
}

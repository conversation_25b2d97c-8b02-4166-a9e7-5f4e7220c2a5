import { z } from "zod";
import { Database } from "../supabase/database.types";

export type TaskStatus = "pending" | "in_progress" | "completed" | "cancelled";

export type TaskPriority = "low" | "medium" | "high" | "urgent";

export type TaskWithRelations = Database["tenants"]["Tables"]["tasks"]["Row"] & {
  // Only including case relation since assignee relation is problematic
  case?: {
    id: string;
    title: string;
  } | null;
};

export type CreateTaskInput = {
  title: string;
  description?: string;
  status: TaskStatus;
  due_date?: string;
  assigned_to?: string;
  related_case?: string;
  ai_metadata?: Record<string, unknown>;
};

export type UpdateTaskInput = Partial<CreateTaskInput>;

// Zod validation schema for task creation
export const createTaskSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  status: z.enum(["pending", "in_progress", "completed", "cancelled"]),
  due_date: z.string().optional(),
  assigned_to: z.string().uuid().optional(),
  related_case: z.string().uuid().optional(),
  ai_metadata: z.record(z.any()).optional(),
});

export const updateTaskSchema = createTaskSchema.partial();

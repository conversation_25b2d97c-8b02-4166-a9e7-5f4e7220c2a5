/**
 * Compatibility layer for CopilotKit actions between v1 and AG-UI (v2)
 * This helps manage the transition between the different action formats
 */
import { isAGUIEnabled } from '../features/ag-ui';

// Type definitions to match both v1 and v2 APIs
type LegacyParameter = {
  name: string;
  type: string;
  description: string;
  required?: boolean;
};

// Helper for legacy action config
export interface ActionConfigLegacy<T = any> {
  name: string;
  description: string;
  parameters: LegacyParameter[];
  handler: (args: T) => Promise<any> | any;
}

// Helper for AG-UI action config
export interface ActionConfigAGUI<T = any> {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, unknown>;
    required: string[];
  };
  handler: (args: T) => Promise<any> | any;
}

/**
 * Creates a compatible action configuration that works with both v1 and v2 APIs
 * @param legacyConfig The v1 action configuration
 * @param aguiConfig The v2 (AG-UI) action configuration
 * @returns A configuration that works with the current enabled API version
 */
export function createCompatAction<T = any>(
  legacyConfig: ActionConfigLegacy<T>,
  aguiConfig: ActionConfigAGUI<T>
) {
  return isAGUIEnabled() ? aguiConfig : legacyConfig;
}

/**
 * Convert a legacy parameter array to an AG-UI JSON schema
 * Useful for transitioning from v1 to v2 format
 * @param params The legacy parameters array
 * @returns An AG-UI compatible JSON schema
 */
export function legacyParamsToSchema(params: LegacyParameter[]): {
  type: 'object';
  properties: Record<string, unknown>;
  required: string[];
} {
  const properties: Record<string, unknown> = {};
  const required: string[] = [];

  params.forEach(param => {
    properties[param.name] = {
      type: param.type,
      description: param.description
    };

    if (param.required) {
      required.push(param.name);
    }
  });

  return {
    type: 'object',
    properties,
    required
  };
}

/**
 * AG-UI Action Utilities
 * 
 * This file provides helper types and utilities for AG-UI action implementations.
 * It is designed to work exclusively with CopilotKit v2.x and the AG-UI protocol.
 */

import { z, ZodError, ZodType } from "zod";

/**
 * A type-safe interface for AG-UI action parameters
 */
export interface ActionParameters {
  type: 'object';
  properties: Record<string, unknown>;
  required: string[];
}

/**
 * Type definition for an AG-UI action handler
 */
export type ActionHandler<T> = (args: T) => Promise<{
  ok: boolean;
  message: string;
  [key: string]: any;
}>;

/**
 * Interface for a successful action response
 */
export interface ActionSuccessResponse {
  ok: boolean;
  message: string;
  [key: string]: any; // Allow additional data
}

/**
 * Configuration interface for AG-UI actions
 */
export interface ActionConfig<T> {
  name: string;
  description: string;
  parameters: ActionParameters;
  handler: ActionHandler<T>;
}

/**
 * Validates action input against a schema
 * 
 * @param input Input data to validate
 * @param schema Zod schema to validate against
 * @returns Validated data
 * @throws Error if validation fails
 */
export function validateActionInput<T>(input: unknown, schema: ZodType<T>): T {
  try {
    // Use safeParse for more flexible validation without throwing
    const result = schema.safeParse(input);
    
    if (!result.success) {
      // Format error messages
      const errorMessages = result.error.errors.map((e: ZodError['errors'][number]) => {
        const path = e.path.length > 0 ? `${e.path.join('.')}: ` : '';
        return `${path}${e.message}`;
      }).join(', ');
      
      throw new Error(`Validation error: ${errorMessages}`);
    }
    
    return result.data;
  } catch (error) {
    // Handle non-Zod errors
    if (!(error instanceof Error)) {
      throw new Error('Unknown validation error');
    }
    throw error;
  }
}

/**
 * Ensures that required fields are present in the input data
 * 
 * @param input Input data to check
 * @param requiredFields Array of required field names
 * @throws Error if any required field is missing
 */
export function ensureRequiredFields(input: Record<string, unknown>, requiredFields: string[]): void {
  const missingFields = requiredFields.filter(field => {
    return input[field] === undefined || input[field] === null || input[field] === '';
  });
  
  if (missingFields.length > 0) {
    throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
  }
}

/**
 * Create a standard error response for AG-UI actions
 * 
 * @param message Error message
 * @param details Optional additional error details
 * @returns A formatted error object
 */
export function createActionError(message: string, details?: any): never {
  const error = new Error(message);
  if (details) {
    (error as any).details = details;
  }
  throw error;
}

/**
 * Create a standard success response for AG-UI actions
 * 
 * @param message Success message
 * @param data Optional additional data to include
 * @returns A formatted success response
 */
export function createActionSuccess(message: string, data?: any): { ok: boolean; message: string; [key: string]: any } {
  return {
    ok: true,
    message,
    ...data
  };
}

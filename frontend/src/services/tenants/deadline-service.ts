import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../../lib/supabase/database.types';
import {
  mapDeadlineFromDb,
  mapDeadlinesFromDb,
  mapDeadlineToDb,
  mapDeadlineUpdateToDb,
  mapDeadlineValidationToDb
} from '../../mappers/tenants/deadline';
import type { Deadline, DeadlineInput } from '../../types/domain/tenants/Deadline';

// Query parameters for deadline service
export interface DeadlineQueryParams {
  startDate?: string;
  endDate?: string;
  caseId?: string;
  validationStatus?: string;
  page?: number;
  limit?: number;
}

export interface DeadlineQueryResult {
  deadlines: Deadline[];
  totalCount: number;
}

/**
 * Service for managing deadlines
 * All database interactions are encapsulated here and proper tenant isolation is enforced
 */
export class DeadlineService {
  constructor(
    private supabase: SupabaseClient<Database>,
    private tenantId: string
  ) {}

  /**
   * Get all deadlines with filters
   */
  async getAll(params: DeadlineQueryParams = {}): Promise<DeadlineQueryResult> {
    const {
      startDate,
      endDate,
      caseId,
      validationStatus,
      page = 1,
      limit = 50
    } = params;

    // Calculate pagination parameters
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    // Create SQL query parameters - we'll use a raw SQL query to avoid TypeScript depth issues
    const queryParams: Record<string, unknown> = { tenant_id: this.tenantId };
    const filterConditions = ['tenant_id = :tenant_id'];

    if (startDate) {
      queryParams.start_date = startDate;
      filterConditions.push('deadline_date >= :start_date');
    }

    if (endDate) {
      queryParams.end_date = endDate;
      filterConditions.push('deadline_date <= :end_date');
    }

    if (caseId) {
      queryParams.case_id = caseId;
      filterConditions.push('case_id = :case_id');
    }

    if (validationStatus) {
      queryParams.validation_status = validationStatus;
      filterConditions.push('validation_status = :validation_status');
    }

    // Build the WHERE clause
    const whereClause = filterConditions.join(' AND ');

    // Execute query - Supabase's built-in count functionality
    const { count } = await this.supabase
      .schema('tenants')
      .from('task_deadlines')
      .select('count', { count: 'exact', head: true })
      .eq('tenant_id', this.tenantId);

    // Execute the main query
    const { data, error } = await this.supabase
      .schema('tenants')
      .from('task_deadlines')
      .select('*, documents:document_id(*), cases:case_id(*)')
      .eq('tenant_id', this.tenantId)
      .order('deadline_date', { ascending: true })
      .range(from, to);

    if (error) {
      console.error('Error fetching deadlines:', error);
      throw new Error(`Failed to fetch deadlines: ${error.message}`);
    }

    return {
      deadlines: mapDeadlinesFromDb(data || []),
      totalCount: count || 0
    };
  }

  /**
   * Get a deadline by ID
   */
  async getById(id: string): Promise<Deadline | null> {
    const { data, error } = await this.supabase
      .schema('tenants')
      .from('task_deadlines')
      .select('*, documents:document_id(*), cases:case_id(*)')
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // Record not found
        return null;
      }
      console.error('Error fetching deadline:', error);
      throw new Error(`Failed to fetch deadline: ${error.message}`);
    }

    return mapDeadlineFromDb(data);
  }

  /**
   * Create a new deadline
   */
  async create(userId: string, input: DeadlineInput, taskId: string): Promise<Deadline> {
    // Require a taskId since the task_deadlines table requires it
    if (!taskId) {
      throw new Error('Task ID is required to create a deadline');
    }

    const deadlineData = mapDeadlineToDb(input, userId, this.tenantId, taskId);

    const { data, error } = await this.supabase
      .schema('tenants')
      .from('task_deadlines')
      .insert(deadlineData)
      .select('*')
      .single();

    if (error) {
      console.error('Error creating deadline:', error);
      throw new Error(`Failed to create deadline: ${error.message}`);
    }

    return mapDeadlineFromDb(data);
  }

  /**
   * Update a deadline
   */
  async update(id: string, userId: string, input: Partial<DeadlineInput>): Promise<Deadline> {
    // First get the original deadline to update its calc_steps correctly
    const { data: originalDeadline, error: fetchError } = await this.supabase
      .schema('tenants')
      .from('task_deadlines')
      .select('*')
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .single();

    if (fetchError) {
      console.error('Error fetching deadline for update:', fetchError);
      throw new Error(`Failed to fetch deadline for update: ${fetchError.message}`);
    }

    // Map the update with the original deadline data
    const updateData = mapDeadlineUpdateToDb(input, userId, originalDeadline);

    // Perform the update
    const { data, error } = await this.supabase
      .schema('tenants')
      .from('task_deadlines')
      .update(updateData)
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating deadline:', error);
      throw new Error(`Failed to update deadline: ${error.message}`);
    }

    return mapDeadlineFromDb(data);
  }

  /**
   * Delete a deadline
   */
  async delete(id: string): Promise<void> {
    const { error } = await this.supabase
      .schema('tenants')
      .from('task_deadlines')
      .delete()
      .eq('id', id)
      .eq('tenant_id', this.tenantId);

    if (error) {
      console.error('Error deleting deadline:', error);
      throw new Error(`Failed to delete deadline: ${error.message}`);
    }
  }

  /**
   * Update validation status of a deadline
   */
  async updateValidationStatus(
    id: string,
    userId: string,
    action: 'validate' | 'reject',
    note?: string | null
  ): Promise<Deadline> {
    // First get the original deadline to update its calc_steps correctly
    const { data: originalDeadline, error: fetchError } = await this.supabase
      .schema('tenants')
      .from('task_deadlines')
      .select('*')
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .single();

    if (fetchError) {
      console.error('Error fetching deadline for validation update:', fetchError);
      throw new Error(`Failed to fetch deadline for validation: ${fetchError.message}`);
    }

    // Map the validation update with the original deadline data
    const validationData = mapDeadlineValidationToDb(action, note || null, userId, originalDeadline);

    // Perform the update
    const { data, error } = await this.supabase
      .schema('tenants')
      .from('task_deadlines')
      .update(validationData)
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating deadline validation status:', error);
      throw new Error(`Failed to update deadline validation status: ${error.message}`);
    }

    return mapDeadlineFromDb(data);
  }

  /**
   * Get validation statistics
   */
  async getValidationStats(): Promise<{
    counts: { pending: number; validated: number; rejected: number };
    percentages: { pending: number; validated: number; rejected: number };
    total: number;
  }> {
    // Using separate queries to avoid TypeScript type instantiation depth issues
    const pendingQuery = await this.supabase
      .schema('tenants')
      .from('task_deadlines')
      .select('count', { count: 'exact', head: true })
      .eq('tenant_id', this.tenantId)
      .filter('validation_status', 'eq', 'pending');

    const validatedQuery = await this.supabase
      .schema('tenants')
      .from('task_deadlines')
      .select('count', { count: 'exact', head: true })
      .eq('tenant_id', this.tenantId)
      .filter('validation_status', 'eq', 'validated');

    const rejectedQuery = await this.supabase
      .schema('tenants')
      .from('task_deadlines')
      .select('count', { count: 'exact', head: true })
      .eq('tenant_id', this.tenantId)
      .filter('validation_status', 'eq', 'rejected');

    // Calculate counts
    const pendingCount = pendingQuery.count || 0;
    const validatedCount = validatedQuery.count || 0;
    const rejectedCount = rejectedQuery.count || 0;
    const totalCount = pendingCount + validatedCount + rejectedCount;

    // Calculate percentages
    const pendingPercentage = totalCount > 0 ? Math.round((pendingCount / totalCount) * 100) : 0;
    const validatedPercentage = totalCount > 0 ? Math.round((validatedCount / totalCount) * 100) : 0;
    const rejectedPercentage = totalCount > 0 ? Math.round((rejectedCount / totalCount) * 100) : 0;

    return {
      counts: {
        pending: pendingCount,
        validated: validatedCount,
        rejected: rejectedCount
      },
      percentages: {
        pending: pendingPercentage,
        validated: validatedPercentage,
        rejected: rejectedPercentage
      },
      total: totalCount
    };
  }
}

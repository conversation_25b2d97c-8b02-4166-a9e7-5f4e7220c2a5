// Type definitions for Cypress
/// <reference types="cypress" />

declare global {
  namespace Cypress {
    interface VisitOptions {
      failOnStatusCode?: boolean;
      [key: string]: unknown;
    }

    interface Chainable<Subject = unknown> {
      // Custom commands
      login(email?: string, password?: string): Chainable<void>;
      logout(): Chainable<void>;
      attachFile(filePath: string): Chainable<void>;
      getByTestId(id: string): Chainable<Element>;
      getBySel(selector: string): Chainable<Element>;
      getBySelLike(selector: string): Chainable<Element>;
      navigateAuthenticated(url: string): Chainable<Element>;
      checkAuthentication(): Chainable<Element>;
      shadcnSelect(selector: string, optionText: string): Chainable<Element>;

      // Built-in Cypress commands that might be missing
      session(id: string, setup: () => void, options?: Record<string, unknown>): Chainable<void>;
      intercept(method: string, url: string, response?: unknown): Chainable<Interception>;
      intercept(url: string, response?: unknown): Chainable<Interception>;
      intercept(options: InterceptOptions): Chainable<Interception>;
      intercept(url: string, handler: (_req: CyHttpMessages.IncomingHttpRequest) => void): Chainable<Interception>;
    }

    interface Commands {
      add(name: string, fn: (...args: unknown[]) => unknown, options?: Record<string, unknown>): void;
    }

    interface InterceptOptions {
      method?: string;
      url?: string | RegExp;
      hostname?: string;
      headers?: Record<string, string>;
      query?: Record<string, string>;
      times?: number;
    }

    interface Interception {
      alias?: string;
    }
  }

  // Declare the global Cypress object
  const Cypress: {
    Commands: {
      add(name: string, fn: (...args: unknown[]) => unknown, options?: Record<string, unknown>): void;
    };
    env(key?: string): unknown;
    Blob: typeof Blob;
  };
}

// Extend Chai assertions for Cypress
declare global {
  namespace Chai {
    interface Assertion {
      // Basic matchers
      toBe(expected: unknown): Assertion;
      toBeCloseTo(expected: number, precision?: number): Assertion;
      toBeDefined(): Assertion;
      toBeFalsy(): Assertion;
      toBeGreaterThan(expected: number | bigint): Assertion;
      toBeGreaterThanOrEqual(expected: number | bigint): Assertion;
      toBeInstanceOf(expected: unknown): Assertion;
      toBeLessThan(expected: number | bigint): Assertion;
      toBeLessThanOrEqual(expected: number | bigint): Assertion;
      toBeNaN(): Assertion;
      toBeNull(): Assertion;
      toBeTruthy(): Assertion;
      toBeUndefined(): Assertion;
      toContain(expected: unknown): Assertion;
      toContainEqual(expected: unknown): Assertion;
      toEqual(expected: unknown): Assertion;
      toHaveLength(expected: number): Assertion;
      toHaveProperty(keyPath: string | Array<string>, value?: unknown): Assertion;
      toMatch(expected: string | RegExp): Assertion;
      toMatchObject(expected: object | Array<object>): Assertion;
      toStrictEqual(expected: unknown): Assertion;
      toThrow(expected?: string | Error | RegExp): Assertion;
      toThrowError(expected?: string | Error | RegExp): Assertion;

      // DOM Testing Library matchers
      toBeInTheDocument(): Assertion;
      toBeVisible(): Assertion;
      toBeDisabled(): Assertion;
      toBeEnabled(): Assertion;
      toBeEmpty(): Assertion;
      toBeInvalid(): Assertion;
      toBeRequired(): Assertion;
      toBeValid(): Assertion;
      toBeChecked(): Assertion;
      toHaveAttribute(attr: string, value?: unknown): Assertion;
      toHaveClass(className: string): Assertion;
      toHaveFocus(): Assertion;
      toHaveFormValues(expectedValues: Record<string, unknown>): Assertion;
      toHaveStyle(css: Record<string, unknown>): Assertion;
      toHaveTextContent(text: string | RegExp): Assertion;
      toHaveValue(value: unknown): Assertion;

      // Jest-specific matchers
      toHaveBeenCalled(): Assertion;
      toHaveBeenCalledTimes(expected: number): Assertion;
      toHaveBeenCalledWith(...args: unknown[]): Assertion;
      toHaveBeenLastCalledWith(...args: unknown[]): Assertion;
      toHaveBeenNthCalledWith(nthCall: number, ...args: unknown[]): Assertion;
      toHaveReturned(): Assertion;
      toHaveReturnedTimes(expected: number): Assertion;
      toHaveReturnedWith(expected: unknown): Assertion;
      toHaveLastReturnedWith(expected: unknown): Assertion;
      toHaveNthReturnedWith(nthCall: number, expected: unknown): Assertion;
    }
  }

  namespace Chai {
    interface ExpectStatic {
      // Asymmetric matchers
      any(expectedType?: unknown): unknown;
      anything(): unknown;
      arrayContaining(sample: Array<unknown>): unknown;
      objectContaining(sample: Record<string, unknown>): unknown;
      stringContaining(expected: string): unknown;
      stringMatching(expected: string | RegExp): unknown;
      not: ExpectStatic;
    }
  }
}

export {};

/**
 * Domain model for Cases
 * This is the clean, frontend-friendly model that all components should use
 * Note: This model abstracts away complex database implementation details
 */
export interface Case {
  id: string;
  tenantId: string;
  title: string;
  description: string | null;
  status: CaseStatus;
  practiceArea: string | null;
  caseNumber: string | null;
  courtName: string | null;
  jurisdiction: string | null;
  filingDate: string | null;
  trialDate: string | null;
  primaryAttorneyId: string | null;
  priorityLevel: string | null;
  statueOfLimitations: string | null;
  createdBy: string;
  createdAt: string;
  updatedBy: string | null;
  updatedAt: string | null;

  // Joined data
  primaryAttorney?: {
    id: string;
    fullName?: string | null;
    email?: string | null;
  } | null;

  clients?: {
    id: string;
    fullName: string;
    email?: string | null;
  }[];

  // Counts for related items
  documentCount?: number;
  deadlineCount?: number;
  noteCount?: number;
}

/**
 * Input data for creating or updating a case
 */
export interface CaseInput {
  title: string;
  description?: string | null;
  status?: CaseStatus | null;
  practiceArea?: string | null;
  caseNumber?: string | null;
  courtName?: string | null;
  jurisdiction?: string | null;
  filingDate?: string | null;
  trialDate?: string | null;
  primaryAttorneyId?: string | null;
  priorityLevel?: string | null;
  statueOfLimitations?: string | null;
  clientIds?: string[];
}

/**
 * Enumeration of possible case statuses
 */
export enum CaseStatus {
  OPEN = 'open',
  CLOSED = 'closed',
  PENDING = 'pending',
  ARCHIVED = 'archived',
  SETTLED = 'settled',
  ON_HOLD = 'on_hold',
}

/**
 * Enumeration of possible case priority levels
 */
export enum CasePriorityLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

/**
 * Metadata for case-related interactions
 */
export interface CaseMetadata {
  lastViewed?: string;
  lastActivity?: string;
  activityCount?: number;
  tags?: string[];
  customFields?: Record<string, unknown>;
}

// Type declarations for missing modules

declare module '@jest/globals' {
  export const describe: (name: string, fn: () => void) => void;
  export const it: (name: string, fn: () => void | Promise<void>, timeout?: number) => void;
  export const test: (name: string, fn: () => void | Promise<void>, timeout?: number) => void;
  export const expect: jest.Expect;
  export const beforeAll: (fn: () => void | Promise<void>, timeout?: number) => void;
  export const beforeEach: (fn: () => void | Promise<void>, timeout?: number) => void;
  export const afterAll: (fn: () => void | Promise<void>, timeout?: number) => void;
  export const afterEach: (fn: () => void | Promise<void>, timeout?: number) => void;
  export const jest: jest.Jest;
}

declare module 'react-server-dom-webpack/server.edge' {
  export const createTemporaryReferenceSet: unknown;
  export const renderToReadableStream: ReadableStream;
  export const decodeReply: unknown;
  export const decodeAction: unknown;
  export const decodeFormState: unknown;
}

declare module 'react-server-dom-webpack/static.edge' {
  export const prerender: unknown;
}

declare module 'VAR_MODULE_GLOBAL_ERROR' {
  import { ComponentType } from 'react';
  const GlobalError: ComponentType<{
    error: Error;
    reset: () => void;
  }>;
  export default GlobalError;
}

// Add HeadersIterator type for Next.js
declare global {
  interface HeadersIterator<T> {
    next(): IteratorResult<T>;
    [Symbol.iterator](): HeadersIterator<T>;
  }

  // Fix for Neo4j Integer type
  namespace Neo4j {
    interface Integer {
      toNumber(): number;
      toString(): string;
      valueOf(): number;
    }
  }

  // Declare types for missing properties in webpack
  namespace webpack {
    interface RuleSetUseItem {
      loader: string;
      options?: unknown;
    }
  }
}

declare global {
  interface Window {
    Cypress?: unknown;
  }
}

export {};

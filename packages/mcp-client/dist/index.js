/**
 * MCP Rules Engine Client
 *
 * TypeScript wrapper for the MCP Rules Engine API that provides
 * deadline calculation and health check functionality.
 */
import fetch from 'node-fetch';
import { McpApiError, } from './types';
export * from './types';
export class McpClient {
    constructor(config) {
        // Circuit breaker state
        this.circuitBreakerState = 'closed';
        this.consecutiveFailures = 0;
        this.lastFailureTime = 0;
        this.failureThreshold = 3;
        this.recoveryTimeout = 30000; // 30 seconds
        this.baseUrl = config.baseUrl.replace(/\/$/, ''); // Remove trailing slash
        this.apiKey = config.apiKey;
        this.timeout = config.timeout || 30000; // 30 seconds default
        this.maxRetries = config.maxRetries || 3;
        this.retryDelay = config.retryDelay || 1000; // 1 second base delay
    }
    /**
     * Calculate deadlines for a given jurisdiction and trigger code
     */
    async calculateDeadlines(jurisdiction, triggerCode, startDate, practiceArea) {
        const request = {
            jurisdiction,
            triggerCode,
            startDate,
            practiceArea,
        };
        return this.makeRequest('/api/v1/deadlines/calculate', {
            method: 'POST',
            body: JSON.stringify(request),
        });
    }
    /**
     * Perform health check on the MCP Rules Engine
     */
    async healthCheck() {
        return this.makeRequest('/health');
    }
    /**
     * Make an HTTP request with retry logic, error handling, and circuit breaker
     */
    async makeRequest(endpoint, options = {}) {
        // Check circuit breaker state
        if (this.circuitBreakerState === 'open') {
            const timeSinceLastFailure = Date.now() - this.lastFailureTime;
            if (timeSinceLastFailure < this.recoveryTimeout) {
                throw new McpApiError('Circuit breaker is OPEN - service temporarily unavailable', 503, 'CIRCUIT_BREAKER_OPEN');
            }
            else {
                // Transition to half-open for testing
                this.circuitBreakerState = 'half-open';
            }
        }
        const url = `${this.baseUrl}${endpoint}`;
        const headers = {
            'Content-Type': 'application/json',
            'x-api-key': this.apiKey,
            ...options.headers,
        };
        const requestOptions = {
            ...options,
            headers,
        };
        let lastError = null;
        for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
            try {
                // Create AbortController for timeout
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.timeout);
                const response = await fetch(url, {
                    ...requestOptions,
                    signal: controller.signal,
                });
                clearTimeout(timeoutId);
                if (!response.ok) {
                    const errorBody = await this.safeParseJson(response);
                    throw new McpApiError(errorBody?.message || `HTTP ${response.status}: ${response.statusText}`, response.status, errorBody?.code, errorBody);
                }
                const data = await response.json();
                // Success - reset circuit breaker
                this.onSuccess();
                return data;
            }
            catch (error) {
                lastError = error;
                // Don't retry on client errors (4xx) except for rate limiting (429)
                // Also, 4xx errors (except 429) should NOT count as circuit breaker failures
                if (error instanceof McpApiError &&
                    error.status >= 400 &&
                    error.status < 500 &&
                    error.status !== 429) {
                    // Don't record failure for circuit breaker - these are client errors
                    throw error;
                }
                // If this is the last attempt, record failure and throw the error
                if (attempt === this.maxRetries) {
                    // Record failure for circuit breaker
                    this.onFailure();
                    throw error;
                }
                // Wait before retrying with exponential backoff
                const delay = this.retryDelay * Math.pow(2, attempt);
                await this.sleep(delay);
            }
        }
        // This should never be reached, but just in case
        throw lastError || new Error('Unknown error occurred');
    }
    /**
     * Safely parse JSON response, returning null if parsing fails
     */
    async safeParseJson(response) {
        try {
            return await response.json();
        }
        catch {
            return null;
        }
    }
    /**
     * Sleep for the specified number of milliseconds
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * Handle successful request - reset circuit breaker
     */
    onSuccess() {
        this.consecutiveFailures = 0;
        this.circuitBreakerState = 'closed';
    }
    /**
     * Handle failed request - update circuit breaker state
     */
    onFailure() {
        this.consecutiveFailures++;
        this.lastFailureTime = Date.now();
        if (this.consecutiveFailures >= this.failureThreshold) {
            this.circuitBreakerState = 'open';
            // Circuit breaker opened - could add logging here if needed
            // console.warn(
            //   `MCP Circuit breaker OPENED after ${this.consecutiveFailures} failures. ` +
            //   `Will retry after ${this.recoveryTimeout}ms`
            // );
        }
    }
    /**
     * Get current circuit breaker state for monitoring
     */
    getCircuitBreakerState() {
        return {
            state: this.circuitBreakerState,
            consecutiveFailures: this.consecutiveFailures,
            lastFailureTime: this.lastFailureTime,
        };
    }
}
//# sourceMappingURL=index.js.map